using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using Microsoft.Extensions.Logging;

namespace DataHubGatineau.Application.Services;

/// <summary>
/// Service pour la gestion des domaines d'affaires.
/// </summary>
public class ServiceDomaineAffaires : IServiceDomaineAffaires
{
    private readonly IDepotDomaineAffaires _depotDomaineAffaires;
    private readonly ILogger<ServiceDomaineAffaires> _logger;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="ServiceDomaineAffaires"/>.
    /// </summary>
    /// <param name="depotDomaineAffaires">Dépôt des domaines d'affaires.</param>
    /// <param name="logger">Logger.</param>
    public ServiceDomaineAffaires(
        IDepotDomaineAffaires depotDomaineAffaires,
        ILogger<ServiceDomaineAffaires> logger)
    {
        _depotDomaineAffaires = depotDomaineAffaires;
        _logger = logger;
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<DomaineAffaires>> ObtenirTousAsync()
    {
        try
        {
            _logger.LogInformation("Obtention de tous les domaines d'affaires");
            return await _depotDomaineAffaires.ObtenirTousAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention de tous les domaines d'affaires");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<DomaineAffaires?> ObtenirParIdAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("Obtention du domaine d'affaires avec l'ID {Id}", id);
            return await _depotDomaineAffaires.ObtenirParIdAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention du domaine d'affaires avec l'ID {Id}", id);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<DomaineAffaires> AjouterAsync(DomaineAffaires domaineAffaires)
    {
        try
        {
            // Validation métier
            if (await NomExisteAsync(domaineAffaires.Nom))
            {
                throw new InvalidOperationException($"Un domaine d'affaires avec le nom '{domaineAffaires.Nom}' existe déjà.");
            }

            if (!string.IsNullOrEmpty(domaineAffaires.Code) && await CodeExisteAsync(domaineAffaires.Code))
            {
                throw new InvalidOperationException($"Un domaine d'affaires avec le code '{domaineAffaires.Code}' existe déjà.");
            }

            _logger.LogInformation("Ajout du domaine d'affaires {Nom}", domaineAffaires.Nom);
            return await _depotDomaineAffaires.AjouterAsync(domaineAffaires);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'ajout du domaine d'affaires {Nom}", domaineAffaires.Nom);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<DomaineAffaires> MettreAJourAsync(Guid id, DomaineAffaires domaineAffaires)
    {
        try
        {
            // Validation métier
            if (await NomExisteAsync(domaineAffaires.Nom, id))
            {
                throw new InvalidOperationException($"Un domaine d'affaires avec le nom '{domaineAffaires.Nom}' existe déjà.");
            }

            if (!string.IsNullOrEmpty(domaineAffaires.Code) && await CodeExisteAsync(domaineAffaires.Code, id))
            {
                throw new InvalidOperationException($"Un domaine d'affaires avec le code '{domaineAffaires.Code}' existe déjà.");
            }

            domaineAffaires.Id = id;
            _logger.LogInformation("Mise à jour du domaine d'affaires {Id}", id);
            return await _depotDomaineAffaires.MettreAJourAsync(domaineAffaires);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du domaine d'affaires {Id}", id);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> SupprimerAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("Suppression du domaine d'affaires {Id}", id);
            return await _depotDomaineAffaires.SupprimerAsync(id);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression du domaine d'affaires {Id}", id);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<DomaineAffaires>> ObtenirActifsAsync()
    {
        try
        {
            _logger.LogInformation("Obtention des domaines d'affaires actifs");
            return await _depotDomaineAffaires.ObtenirActifsAsync();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des domaines d'affaires actifs");
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<DomaineAffaires?> ObtenirParCodeAsync(string code)
    {
        try
        {
            _logger.LogInformation("Obtention du domaine d'affaires avec le code {Code}", code);
            return await _depotDomaineAffaires.ObtenirParCodeAsync(code);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention du domaine d'affaires avec le code {Code}", code);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> NomExisteAsync(string nom, Guid? idExclure = null)
    {
        try
        {
            return await _depotDomaineAffaires.NomExisteAsync(nom, idExclure);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification d'existence du nom {Nom}", nom);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> CodeExisteAsync(string code, Guid? idExclure = null)
    {
        try
        {
            return await _depotDomaineAffaires.CodeExisteAsync(code, idExclure);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la vérification d'existence du code {Code}", code);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> ChangerEtatActivationAsync(Guid id, bool estActif)
    {
        try
        {
            _logger.LogInformation("Changement d'état d'activation du domaine d'affaires {Id} vers {EstActif}", id, estActif);
            return await _depotDomaineAffaires.ChangerEtatActivationAsync(id, estActif);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du changement d'état d'activation du domaine d'affaires {Id}", id);
            throw;
        }
    }

    /// <inheritdoc/>
    public async Task<StatistiquesDomaineAffaires> ObtenirStatistiquesAsync(Guid id)
    {
        try
        {
            _logger.LogInformation("Obtention des statistiques du domaine d'affaires {Id}", id);
            var (nombreTermes, datePremier, dateDernier) = await _depotDomaineAffaires.ObtenirStatistiquesUtilisationAsync(id);
            
            return new StatistiquesDomaineAffaires
            {
                NombreTermesGlossaire = nombreTermes,
                DatePremierTerme = datePremier,
                DateDernierTerme = dateDernier
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de l'obtention des statistiques du domaine d'affaires {Id}", id);
            throw;
        }
    }


}
