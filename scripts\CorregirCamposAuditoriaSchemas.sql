-- Script para corregir los campos de auditoría vacíos en los esquemas de metadatos
-- Ejecutar este script para actualizar los registros existentes

USE [Gouvernance]
GO

SET QUOTED_IDENTIFIER ON
SET ANSI_NULLS ON
GO

-- Actualizar los esquemas de metadatos con campos de auditoría vacíos
UPDATE [Metadonnees].[SchemasMetadonnees]
SET 
    [CreePar] = 'Utilisateur',
    [ModifiePar] = 'Utilisateur'
WHERE 
    [CreePar] IS NULL 
    OR [CreePar] = '' 
    OR [ModifiePar] IS NULL 
    OR [ModifiePar] = '';

-- Actualizar las definiciones de metadatos con campos de auditoría vacíos
UPDATE [Metadonnees].[DefinitionsMetadonnees]
SET 
    [CreePar] = 'Utilisateur',
    [ModifiePar] = 'Utilisateur'
WHERE 
    [CreePar] IS NULL 
    OR [CreePar] = '' 
    OR [ModifiePar] IS NULL 
    OR [ModifiePar] = '';

-- Verificar los resultados
SELECT 
    'SchemasMetadonnees' as Tabla,
    COUNT(*) as Total,
    COUNT(CASE WHEN [CreePar] IS NOT NULL AND [CreePar] != '' THEN 1 END) as ConCreePar,
    COUNT(CASE WHEN [ModifiePar] IS NOT NULL AND [ModifiePar] != '' THEN 1 END) as ConModifiePar
FROM [Metadonnees].[SchemasMetadonnees]

UNION ALL

SELECT 
    'DefinitionsMetadonnees' as Tabla,
    COUNT(*) as Total,
    COUNT(CASE WHEN [CreePar] IS NOT NULL AND [CreePar] != '' THEN 1 END) as ConCreePar,
    COUNT(CASE WHEN [ModifiePar] IS NOT NULL AND [ModifiePar] != '' THEN 1 END) as ConModifiePar
FROM [Metadonnees].[DefinitionsMetadonnees];

-- Mostrar los esquemas actualizados
SELECT 
    [Id],
    [Nom],
    [TypeActif],
    [Version],
    [EstActif],
    [CreePar],
    [ModifiePar],
    [DateCreation],
    [DateModification]
FROM [Metadonnees].[SchemasMetadonnees]
ORDER BY [DateModification] DESC;
