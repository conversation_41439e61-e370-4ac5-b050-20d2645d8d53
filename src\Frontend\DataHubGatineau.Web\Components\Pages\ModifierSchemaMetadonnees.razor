@page "/schemas-metadonnees/modifier/{Id:guid}"
@using DataHubGatineau.Web.Models.Metadata
@using DataHubGatineau.Web.Services.Interfaces
@inject ISchemaMetadonneesService SchemaMetadonneesService
@inject ITypeActifDonneesService TypeActifDonneesService
@inject NavigationManager NavigationManager

<PageTitle>Modifier un Schéma de Métadonnées - DataHub Gatineau</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Modifier un Schéma de Métadonnées</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/schemas-metadonnees">Schémas de métadonnées</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Modifier</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="/schemas-metadonnees" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Retour à la liste
                    </a>
                </div>
            </div>

            @if (_loading)
            {
                <div class="d-flex justify-content-center my-5">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (_schema == null)
            {
                <div class="alert alert-danger">
                    <h4>Schéma non trouvé</h4>
                    <p>Le schéma de métadonnées demandé n'existe pas ou a été supprimé.</p>
                </div>
            }
            else
            {
                <!-- Formulaire de modification -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-pencil me-2"></i>
                            Informations du schéma
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="_schema" OnValidSubmit="SauvegarderSchema">
                            <DataAnnotationsValidator />
                            <ValidationSummary class="alert alert-danger" />

                            @if (!string.IsNullOrEmpty(_errorMessage))
                            {
                                <div class="alert alert-danger">
                                    @_errorMessage
                                </div>
                            }

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nom" class="form-label">
                                            Nom <span class="text-danger">*</span>
                                        </label>
                                        <InputText id="nom" class="form-control" @bind-Value="_schema.Nom" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _schema.Nom)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="typeActif" class="form-label">
                                            Type d'actif <span class="text-danger">*</span>
                                        </label>
                                        <InputSelect id="typeActif" class="form-select" @bind-Value="_schema.TypeActif">
                                            <option value="">-- Sélectionner un type d'actif --</option>
                                            @foreach (var type in _typesActifs)
                                            {
                                                <option value="@type">@type</option>
                                            }
                                        </InputSelect>
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _schema.TypeActif)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="version" class="form-label">
                                            Version <span class="text-danger">*</span>
                                        </label>
                                        <InputText id="version" class="form-control" @bind-Value="_schema.Version" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _schema.Version)" />
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <InputTextArea id="description" class="form-control" rows="4" @bind-Value="_schema.Description" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _schema.Description)" />
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <InputCheckbox id="estActif" class="form-check-input" @bind-Value="_schema.EstActif" />
                                            <label class="form-check-label" for="estActif">
                                                Schéma actif
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end gap-2">
                                <a href="/schemas-metadonnees" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary" disabled="@_saving">
                                    @if (_saving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    }
                                    else
                                    {
                                        <i class="bi bi-check-circle me-2"></i>
                                    }
                                    Sauvegarder
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>

                <!-- Définitions de métadonnées -->
                @if (_schema != null)
                {
                    <div class="card mt-4">
                        <div class="card-header d-flex justify-content-between align-items-center">
                            <h5 class="card-title mb-0">
                                <i class="bi bi-list-ul me-2"></i>
                                Définitions de métadonnées (@(_schema.DefinitionsMetadonnees?.Count ?? 0))
                            </h5>
                            <button class="btn btn-primary btn-sm" @onclick="AjouterDefinition">
                                <i class="bi bi-plus-circle"></i> Ajouter une définition
                            </button>
                        </div>
                        <div class="card-body">
                            @if (_schema.DefinitionsMetadonnees == null || !_schema.DefinitionsMetadonnees.Any())
                            {
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle me-2"></i>
                                    Aucune définition de métadonnée n'a été configurée pour ce schéma.
                                </div>
                            }
                            else
                            {
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Nom</th>
                                                <th>Type de donnée</th>
                                                <th>Obligatoire</th>
                                                <th>Ordre</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            @foreach (var definition in _schema.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
                                            {
                                                <tr>
                                                    <td>
                                                        <strong>@definition.Nom</strong>
                                                        @if (!string.IsNullOrEmpty(definition.Description))
                                                        {
                                                            <br />
                                                            <small class="text-muted">@definition.Description</small>
                                                        }
                                                    </td>
                                                    <td>
                                                        <span class="badge bg-secondary">@definition.TypeDonnee</span>
                                                    </td>
                                                    <td>
                                                        @if (definition.EstObligatoire)
                                                        {
                                                            <span class="badge bg-danger">Obligatoire</span>
                                                        }
                                                        else
                                                        {
                                                            <span class="badge bg-success">Optionnel</span>
                                                        }
                                                    </td>
                                                    <td>@definition.Ordre</td>
                                                    <td>
                                                        <div class="btn-group" role="group">
                                                            <button class="btn btn-sm btn-warning" @onclick="() => ModifierDefinition(definition.Id)">
                                                                <i class="bi bi-pencil"></i>
                                                            </button>
                                                            <button class="btn btn-sm btn-danger" @onclick="() => SupprimerDefinition(definition.Id)">
                                                                <i class="bi bi-trash"></i>
                                                            </button>
                                                        </div>
                                                    </td>
                                                </tr>
                                            }
                                        </tbody>
                                    </table>
                                </div>
                            }
                        </div>
                    </div>
                }
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public Guid Id { get; set; }
    
    private SchemaMetadonnees? _schema;
    private bool _loading = true;
    private bool _saving = false;
    private string _errorMessage = string.Empty;
    private List<string> _typesActifs = new();

    protected override async Task OnInitializedAsync()
    {
        await ChargerTypesActifs();
        await ChargerSchema();
    }

    private async Task ChargerSchema()
    {
        _loading = true;
        try
        {
            _schema = await SchemaMetadonneesService.ObtenirParIdAsync(Id);
            if (_schema == null)
            {
                _errorMessage = "Schéma non trouvé.";
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement du schéma: {ex.Message}");
            _errorMessage = "Erreur lors du chargement du schéma.";
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task ChargerTypesActifs()
    {
        try
        {
            var typesActifs = await TypeActifDonneesService.ObtenirTousAsync();
            _typesActifs = typesActifs.Select(t => t.Nom).OrderBy(n => n).ToList();
            
            // Si aucun type n'est trouvé, utiliser une liste par défaut
            if (!_typesActifs.Any())
            {
                _typesActifs = new List<string> 
                { 
                    "Table", "Vue", "Procédure", "Fonction", "Fichier", 
                    "API", "Rapport", "Dashboard" 
                };
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des types d'actifs: {ex.Message}");
            // En cas d'erreur, utiliser une liste par défaut
            _typesActifs = new List<string> 
            { 
                "Table", "Vue", "Procédure", "Fonction", "Fichier", 
                "API", "Rapport", "Dashboard" 
            };
        }
    }

    private async Task SauvegarderSchema()
    {
        if (_schema == null) return;

        _saving = true;
        _errorMessage = string.Empty;

        try
        {
            _schema.DateModification = DateTime.Now;
            
            var schemaModifie = await SchemaMetadonneesService.MettreAJourAsync(Id, _schema);
            
            if (schemaModifie != null)
            {
                NavigationManager.NavigateTo($"/schemas-metadonnees/{Id}");
            }
            else
            {
                _errorMessage = "Erreur lors de la sauvegarde du schéma.";
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la sauvegarde: {ex.Message}");
            _errorMessage = "Erreur lors de la sauvegarde du schéma.";
        }
        finally
        {
            _saving = false;
        }
    }

    private void AjouterDefinition()
    {
        NavigationManager.NavigateTo($"/schemas-metadonnees/{Id}/definitions/ajouter");
    }

    private void ModifierDefinition(Guid definitionId)
    {
        NavigationManager.NavigateTo($"/schemas-metadonnees/{Id}/definitions/modifier/{definitionId}");
    }

    private async Task SupprimerDefinition(Guid definitionId)
    {
        try
        {
            var supprime = await SchemaMetadonneesService.SupprimerDefinitionMetadonneeAsync(definitionId);
            if (supprime)
            {
                // Recharger le schéma pour mettre à jour la liste des définitions
                await ChargerSchema();
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la suppression de la définition: {ex.Message}");
            _errorMessage = "Erreur lors de la suppression de la définition.";
        }
    }
}
