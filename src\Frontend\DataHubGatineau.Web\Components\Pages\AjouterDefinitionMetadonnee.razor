@page "/schemas-metadonnees/{SchemaId:guid}/definitions/ajouter"
@using DataHubGatineau.Web.Models.Metadata
@using DataHubGatineau.Web.Services.Interfaces
@inject ISchemaMetadonneesService SchemaMetadonneesService
@inject NavigationManager NavigationManager

<PageTitle>Ajouter une Définition de Métadonnée - DataHub Gatineau</PageTitle>

<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- En-tête -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h1>Ajouter une Définition de Métadonnée</h1>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="/schemas-metadonnees">Schémas de métadonnées</a></li>
                            <li class="breadcrumb-item"><a href="/schemas-metadonnees/@SchemaId">Détails du schéma</a></li>
                            <li class="breadcrumb-item active" aria-current="page">Ajouter définition</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <a href="/schemas-metadonnees/@SchemaId" class="btn btn-outline-secondary">
                        <i class="bi bi-arrow-left"></i> Retour au schéma
                    </a>
                </div>
            </div>

            @if (_loading)
            {
                <div class="d-flex justify-content-center my-5">
                    <div class="spinner-border" role="status">
                        <span class="visually-hidden">Chargement...</span>
                    </div>
                </div>
            }
            else if (_schema == null)
            {
                <div class="alert alert-danger">
                    <h4>Schéma non trouvé</h4>
                    <p>Le schéma de métadonnées demandé n'existe pas ou a été supprimé.</p>
                </div>
            }
            else
            {
                <!-- Informations du schéma -->
                <div class="alert alert-info mb-4">
                    <h6><i class="bi bi-info-circle me-2"></i>Schéma: @_schema.Nom</h6>
                    <small>Type d'actif: @_schema.TypeActif | Version: @_schema.Version</small>
                </div>

                <!-- Formulaire d'ajout -->
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-plus-circle me-2"></i>
                            Nouvelle définition de métadonnée
                        </h5>
                    </div>
                    <div class="card-body">
                        <EditForm Model="_definition" OnValidSubmit="SauvegarderDefinition">
                            <DataAnnotationsValidator />
                            <ValidationSummary class="alert alert-danger" />

                            @if (!string.IsNullOrEmpty(_errorMessage))
                            {
                                <div class="alert alert-danger">
                                    @_errorMessage
                                </div>
                            }

                            <div class="row">
                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="nom" class="form-label">
                                            Nom <span class="text-danger">*</span>
                                        </label>
                                        <InputText id="nom" class="form-control" @bind-Value="_definition.Nom" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _definition.Nom)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="typeDonnee" class="form-label">
                                            Type de données <span class="text-danger">*</span>
                                        </label>
                                        <InputSelect id="typeDonnee" class="form-select" @bind-Value="_definition.TypeDonnee" @onchange="OnTypeChange">
                                            <option value="">-- Sélectionner un type --</option>
                                            <option value="Texte">Texte</option>
                                            <option value="Nombre">Nombre</option>
                                            <option value="Date">Date</option>
                                            <option value="Booléen">Booléen</option>
                                            <option value="Liste">Liste</option>
                                            <option value="URL">URL</option>
                                            <option value="Email">Email</option>
                                        </InputSelect>
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _definition.TypeDonnee)" />
                                    </div>

                                    @* Éditeur d'options pour les listes *@
                                    @if (_definition.TypeDonnee == "Liste")
                                    {
                                        <div class="mb-3">
                                            <label class="form-label">
                                                Options de la liste <span class="text-danger">*</span>
                                            </label>
                                            <div class="card">
                                                <div class="card-body">
                                                    <div class="mb-3">
                                                        <div class="input-group">
                                                            <input type="text" class="form-control" @bind="_nouvelleOption" @onkeypress="OnKeyPressOption" placeholder="Ajouter une option..." />
                                                            <button type="button" class="btn btn-outline-primary" @onclick="AjouterOption">
                                                                <i class="bi bi-plus"></i> Ajouter
                                                            </button>
                                                        </div>
                                                    </div>

                                                    @if (_optionsListe.Any())
                                                    {
                                                        <div class="list-group">
                                                            @for (int i = 0; i < _optionsListe.Count; i++)
                                                            {
                                                                var index = i; // Capture pour éviter les problèmes de closure
                                                                <div class="list-group-item d-flex justify-content-between align-items-center">
                                                                    <span>@_optionsListe[index]</span>
                                                                    <button type="button" class="btn btn-sm btn-outline-danger" @onclick="() => SupprimerOption(index)">
                                                                        <i class="bi bi-trash"></i>
                                                                    </button>
                                                                </div>
                                                            }
                                                        </div>
                                                    }
                                                    else
                                                    {
                                                        <div class="text-muted">
                                                            <i class="bi bi-info-circle me-2"></i>
                                                            Aucune option définie. Ajoutez au moins une option pour la liste.
                                                        </div>
                                                    }
                                                </div>
                                            </div>
                                        </div>
                                    }

                                    <div class="mb-3">
                                        <label for="ordre" class="form-label">
                                            Ordre d'affichage <span class="text-danger">*</span>
                                        </label>
                                        <InputNumber id="ordre" class="form-control" @bind-Value="_definition.Ordre" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _definition.Ordre)" />
                                    </div>
                                </div>

                                <div class="col-md-6">
                                    <div class="mb-3">
                                        <label for="description" class="form-label">Description</label>
                                        <InputTextArea id="description" class="form-control" rows="3" @bind-Value="_definition.Description" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _definition.Description)" />
                                    </div>

                                    <div class="mb-3">
                                        <label for="valeurParDefaut" class="form-label">Valeur par défaut</label>
                                        <InputText id="valeurParDefaut" class="form-control" @bind-Value="_definition.ValeurParDefaut" />
                                        <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _definition.ValeurParDefaut)" />
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <InputCheckbox id="estObligatoire" class="form-check-input" @bind-Value="_definition.EstObligatoire" />
                                            <label class="form-check-label" for="estObligatoire">
                                                Champ obligatoire
                                            </label>
                                        </div>
                                    </div>

                                    <div class="mb-3">
                                        <div class="form-check">
                                            <InputCheckbox id="estCalcule" class="form-check-input" @bind-Value="_definition.EstCalcule" />
                                            <label class="form-check-label" for="estCalcule">
                                                Valeur calculée automatiquement
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-end gap-2">
                                <a href="/schemas-metadonnees/@SchemaId" class="btn btn-secondary">
                                    <i class="bi bi-x-circle"></i> Annuler
                                </a>
                                <button type="submit" class="btn btn-primary" disabled="@_saving">
                                    @if (_saving)
                                    {
                                        <span class="spinner-border spinner-border-sm me-2" role="status"></span>
                                    }
                                    else
                                    {
                                        <i class="bi bi-check-circle me-2"></i>
                                    }
                                    Ajouter la définition
                                </button>
                            </div>
                        </EditForm>
                    </div>
                </div>
            }
        </div>
    </div>
</div>

@code {
    [Parameter] public Guid SchemaId { get; set; }

    private SchemaMetadonnees? _schema;
    private DefinitionMetadonnee _definition = new();
    private bool _loading = true;
    private bool _saving = false;
    private string _errorMessage = string.Empty;

    // Variables pour la gestion des listes
    private List<string> _optionsListe = new();
    private string _nouvelleOption = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await ChargerSchema();
        InitialiserDefinition();
    }

    private async Task ChargerSchema()
    {
        _loading = true;
        try
        {
            _schema = await SchemaMetadonneesService.ObtenirParIdAsync(SchemaId);
            if (_schema == null)
            {
                _errorMessage = "Schéma non trouvé.";
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement du schéma: {ex.Message}");
            _errorMessage = "Erreur lors du chargement du schéma.";
        }
        finally
        {
            _loading = false;
        }
    }

    private void InitialiserDefinition()
    {
        _definition = new DefinitionMetadonnee
        {
            Id = Guid.NewGuid(),
            SchemaMetadonneesId = SchemaId,
            Ordre = 1,
            EstObligatoire = false,
            EstCalcule = false,
            DateCreation = DateTime.Now,
            DateModification = DateTime.Now
        };
    }

    private async Task SauvegarderDefinition()
    {
        if (_schema == null) return;

        _saving = true;
        _errorMessage = string.Empty;

        try
        {
            // Assigner l'ID du schéma à la définition
            _definition.SchemaMetadonneesId = SchemaId;
            _definition.DateModification = DateTime.Now;

            // Si c'est une liste, sauvegarder les options dans ReglesValidation
            if (_definition.TypeDonnee == "Liste" && _optionsListe.Any())
            {
                var optionsJson = System.Text.Json.JsonSerializer.Serialize(new { options = _optionsListe });
                _definition.ReglesValidation = optionsJson;
            }

            // Appeler le service pour ajouter la définition
            var nouvelleDefinition = await SchemaMetadonneesService.AjouterDefinitionMetadonneeAsync(SchemaId, _definition);

            if (nouvelleDefinition != null)
            {
                // Rediriger vers la page de détails du schéma
                NavigationManager.NavigateTo($"/schemas-metadonnees/{SchemaId}");
            }
            else
            {
                _errorMessage = "Erreur lors de la sauvegarde de la définition.";
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la sauvegarde: {ex.Message}");
            _errorMessage = $"Erreur lors de la sauvegarde de la définition: {ex.Message}";
        }
        finally
        {
            _saving = false;
        }
    }

    private void OnTypeChange(ChangeEventArgs e)
    {
        var nouveauType = e.Value?.ToString() ?? string.Empty;
        _definition.TypeDonnee = nouveauType;

        // Réinitialiser les options si on change de type
        if (nouveauType != "Liste")
        {
            _optionsListe.Clear();
            _nouvelleOption = string.Empty;
            _definition.ReglesValidation = null;
        }
    }

    private void AjouterOption()
    {
        if (!string.IsNullOrWhiteSpace(_nouvelleOption) && !_optionsListe.Contains(_nouvelleOption.Trim()))
        {
            _optionsListe.Add(_nouvelleOption.Trim());
            _nouvelleOption = string.Empty;
        }
    }

    private void SupprimerOption(int index)
    {
        if (index >= 0 && index < _optionsListe.Count)
        {
            _optionsListe.RemoveAt(index);
        }
    }

    private async Task OnKeyPressOption(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            AjouterOption();
        }
    }
}
