@page "/termes-glossaire/ajouter"
@page "/termes-glossaire/modifier/{Id:guid}"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@inject ITermeGlossaireService TermeGlossaireService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>@(_isEdit ? "Modifier" : "Ajouter") un terme - Glossaire - DataHub Gatineau</PageTitle>

<!-- Breadcrumb -->
<nav aria-label="breadcrumb" class="mb-4">
    <ol class="breadcrumb">
        <li class="breadcrumb-item"><a href="/">Accueil</a></li>
        <li class="breadcrumb-item"><a href="/glossaire">Glossaire</a></li>
        <li class="breadcrumb-item active" aria-current="page">@(_isEdit ? "Modifier" : "Ajouter") un terme</li>
    </ol>
</nav>

<!-- Header -->
<div class="d-flex justify-content-between align-items-center mb-4">
    <div>
        <h1 class="h3 mb-0">
            <i class="bi bi-@(_isEdit ? "pencil" : "plus-circle") text-primary me-2"></i>
            @(_isEdit ? "Modifier" : "Ajouter") un terme
        </h1>
        <p class="text-muted mb-0">@(_isEdit ? "Modifiez les informations du terme" : "Ajoutez un nouveau terme au glossaire")</p>
    </div>
    <div>
        <a href="/glossaire" class="btn btn-outline-secondary">
            <i class="bi bi-arrow-left me-1"></i>
            Retour au glossaire
        </a>
    </div>
</div>

@if (_loading)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else
{
    <div class="row">
        <div class="col-lg-8">
            <EditForm Model="_terme" OnValidSubmit="SauvegarderTerme">
                <DataAnnotationsValidator />
                
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-info-circle me-2"></i>
                            Informations générales
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Nom du terme -->
                        <div class="mb-3">
                            <label for="nom" class="form-label">
                                Nom du terme <span class="text-danger">*</span>
                            </label>
                            <InputText id="nom" class="form-control" @bind-Value="_terme.Nom" placeholder="Entrez le nom du terme" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _terme.Nom)" class="text-danger" />
                        </div>

                        <!-- Définition -->
                        <div class="mb-3">
                            <label for="definition" class="form-label">
                                Définition <span class="text-danger">*</span>
                            </label>
                            <InputTextArea id="definition" class="form-control" rows="4" @bind-Value="_terme.Definition"
                                         placeholder="Entrez la définition complète du terme" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _terme.Definition)" class="text-danger" />
                            <div class="form-text">Décrivez clairement et précisément ce que signifie ce terme.</div>
                        </div>

                        <!-- Propriétaire -->
                        <div class="mb-3">
                            <label for="proprietaire" class="form-label">
                                Propriétaire <span class="text-danger">*</span>
                            </label>
                            <InputText id="proprietaire" class="form-control" @bind-Value="_terme.Proprietaire"
                                     placeholder="Nom du propriétaire ou responsable" />
                            <Microsoft.AspNetCore.Components.Forms.ValidationMessage For="@(() => _terme.Proprietaire)" class="text-danger" />
                        </div>

                        <!-- Domaine de gouvernance -->
                        <div class="mb-3">
                            <label for="domaine" class="form-label">Domaine de gouvernance</label>
                            <InputSelect id="domaine" class="form-select" @bind-Value="_selectedDomaineId">
                                <option value="">Sélectionnez un domaine (optionnel)</option>
                                @foreach (var domaine in _domaines)
                                {
                                    <option value="@domaine.Id">@domaine.Nom</option>
                                }
                            </InputSelect>
                        </div>

                        <!-- Domaine d'affaires -->
                        <div class="mb-3">
                            <label for="domaineAffaires" class="form-label">Domaine d'affaires</label>
                            <InputText id="domaineAffaires" class="form-control" @bind-Value="_terme.DomaineAffaires" 
                                     placeholder="Ex: Finance, RH, IT, Marketing..." />
                            <div class="form-text">Spécifiez le secteur d'activité ou département concerné.</div>
                        </div>
                    </div>
                </div>

                <div class="card mt-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-list-ul me-2"></i>
                            Détails complémentaires
                        </h5>
                    </div>
                    <div class="card-body">
                        <!-- Exemples -->
                        <div class="mb-3">
                            <label for="exemples" class="form-label">Exemples d'utilisation</label>
                            <InputTextArea id="exemples" class="form-control" rows="3" @bind-Value="_terme.Exemples" 
                                         placeholder="Donnez des exemples concrets d'utilisation de ce terme" />
                            <div class="form-text">Aidez les utilisateurs à comprendre le contexte d'utilisation.</div>
                        </div>

                        <!-- Synonymes -->
                        <div class="mb-3">
                            <label for="synonymes" class="form-label">Synonymes</label>
                            <InputText id="synonymes" class="form-control" @bind-Value="_terme.Synonymes" 
                                     placeholder="Terme1, Terme2, Terme3..." />
                            <div class="form-text">Séparez les synonymes par des virgules.</div>
                        </div>

                        <!-- Terme parent -->
                        <div class="mb-3">
                            <label for="termeParent" class="form-label">Terme parent</label>
                            <InputSelect id="termeParent" class="form-select" @bind-Value="_selectedTermeParentId">
                                <option value="">Aucun terme parent</option>
                                @foreach (var terme in _termesDisponibles.Where(t => t.Id != _terme.Id))
                                {
                                    <option value="@terme.Id">@terme.Nom</option>
                                }
                            </InputSelect>
                            <div class="form-text">Sélectionnez un terme parent si ce terme est une spécialisation d'un autre.</div>
                        </div>
                    </div>
                </div>

                <!-- Actions -->
                <div class="d-flex justify-content-between mt-4">
                    <div>
                        <a href="/glossaire" class="btn btn-outline-secondary">
                            <i class="bi bi-x-circle me-1"></i>
                            Annuler
                        </a>
                    </div>
                    <div>
                        @if (_isEdit)
                        {
                            <button type="button" class="btn btn-outline-danger me-2" @onclick="SupprimerTerme">
                                <i class="bi bi-trash me-1"></i>
                                Supprimer
                            </button>
                        }
                        <button type="submit" class="btn btn-primary" disabled="@_saving">
                            @if (_saving)
                            {
                                <span class="spinner-border spinner-border-sm me-1" role="status"></span>
                            }
                            else
                            {
                                <i class="bi bi-@(_isEdit ? "check" : "plus-circle") me-1"></i>
                            }
                            @(_isEdit ? "Mettre à jour" : "Ajouter le terme")
                        </button>
                    </div>
                </div>
            </EditForm>
        </div>

        <!-- Sidebar avec aide -->
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-question-circle me-2"></i>
                        Aide
                    </h5>
                </div>
                <div class="card-body">
                    <div class="help-section mb-3">
                        <h6><i class="bi bi-lightbulb text-warning me-1"></i> Conseils</h6>
                        <ul class="small">
                            <li>Utilisez un nom court et précis</li>
                            <li>La définition doit être claire et complète</li>
                            <li>Ajoutez des exemples pour faciliter la compréhension</li>
                            <li>Utilisez les synonymes pour améliorer la recherche</li>
                        </ul>
                    </div>

                    <div class="help-section mb-3">
                        <h6><i class="bi bi-info-circle text-info me-1"></i> Bonnes pratiques</h6>
                        <ul class="small">
                            <li>Évitez les acronymes sans explication</li>
                            <li>Restez neutre et objectif</li>
                            <li>Vérifiez l'unicité du terme</li>
                            <li>Associez au bon domaine de gouvernance</li>
                        </ul>
                    </div>

                    @if (_isEdit)
                    {
                        <div class="help-section">
                            <h6><i class="bi bi-clock text-muted me-1"></i> Historique</h6>
                            <p class="small text-muted">
                                Créé le: @_terme.DateCreation.ToString("dd/MM/yyyy")<br>
                                Modifié le: @_terme.DateModification.ToString("dd/MM/yyyy")
                            </p>
                        </div>
                    }
                </div>
            </div>
        </div>
    </div>
}

<style>
    .help-section h6 {
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 0.5rem;
    }

    .help-section ul {
        margin-bottom: 0;
        padding-left: 1.2rem;
    }

    .help-section li {
        margin-bottom: 0.25rem;
        color: #6c757d;
    }

    .form-label {
        font-weight: 500;
        color: #495057;
    }

    .text-danger {
        font-size: 0.875rem;
    }

    .card-header {
        background-color: #f8f9fa;
        border-bottom: 1px solid #dee2e6;
    }

    .btn-primary {
        min-width: 150px;
    }
</style>

@code {
    [Parameter] public Guid? Id { get; set; }
    [SupplyParameterFromQuery] public Guid? Parent { get; set; }

    private TermeGlossaire _terme = new();
    private List<DomaineGouvernance> _domaines = new();
    private List<TermeGlossaire> _termesDisponibles = new();
    private bool _loading = true;
    private bool _saving = false;
    private bool _isEdit => Id.HasValue;
    private string _selectedDomaineId = string.Empty;
    private string _selectedTermeParentId = string.Empty;

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        _loading = true;
        try
        {
            // Charger les domaines de gouvernance
            var domaines = await DomaineGouvernanceService.ObtenirTousAsync();
            _domaines = domaines.ToList();

            // Charger tous les termes pour la sélection du parent
            var termes = await TermeGlossaireService.ObtenirTousAsync();
            _termesDisponibles = termes.ToList();

            if (_isEdit && Id.HasValue)
            {
                // Mode édition: charger le terme existant
                _terme = await TermeGlossaireService.ObtenirParIdAsync(Id.Value);
                if (_terme != null)
                {
                    _selectedDomaineId = _terme.DomaineGouvernanceId?.ToString() ?? string.Empty;
                    _selectedTermeParentId = _terme.TermeParentId?.ToString() ?? string.Empty;
                }
            }
            else
            {
                // Mode création: nouveau terme
                _terme = new TermeGlossaire
                {
                    Id = Guid.NewGuid(),
                    DateCreation = DateTime.Now,
                    DateModification = DateTime.Now
                };

                // Si un parent est spécifié dans l'URL
                if (Parent.HasValue)
                {
                    _selectedTermeParentId = Parent.Value.ToString();
                }
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des données: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", "Erreur lors du chargement des données.");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task SauvegarderTerme()
    {
        _saving = true;
        try
        {
            // Mettre à jour les relations
            _terme.DomaineGouvernanceId = !string.IsNullOrEmpty(_selectedDomaineId) && Guid.TryParse(_selectedDomaineId, out var domaineId)
                ? domaineId
                : null;

            _terme.TermeParentId = !string.IsNullOrEmpty(_selectedTermeParentId) && int.TryParse(_selectedTermeParentId, out var parentId)
                ? parentId
                : null;

            _terme.DateModification = DateTime.Now;

            if (_isEdit)
            {
                // Mettre à jour le terme existant
                await TermeGlossaireService.MettreAJourAsync(_terme.Id, _terme);
                await JSRuntime.InvokeVoidAsync("alert", "Terme mis à jour avec succès!");
                NavigationManager.NavigateTo($"/termes-glossaire/{_terme.Id}");
            }
            else
            {
                // Créer un nouveau terme
                var nouveauTerme = await TermeGlossaireService.AjouterAsync(_terme);
                await JSRuntime.InvokeVoidAsync("alert", "Terme ajouté avec succès!");
                NavigationManager.NavigateTo($"/termes-glossaire/{nouveauTerme.Id}");
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de la sauvegarde: {ex.Message}");
            await JSRuntime.InvokeVoidAsync("alert", $"Erreur lors de la sauvegarde: {ex.Message}");
        }
        finally
        {
            _saving = false;
        }
    }

    private async Task SupprimerTerme()
    {
        if (!_isEdit || _terme == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Êtes-vous sûr de vouloir supprimer le terme '{_terme.Nom}' ?\n\nCette action est irréversible.");

        if (confirmed)
        {
            try
            {
                await TermeGlossaireService.SupprimerAsync(_terme.Id);
                await JSRuntime.InvokeVoidAsync("alert", "Terme supprimé avec succès!");
                NavigationManager.NavigateTo("/glossaire");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Erreur lors de la suppression: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression du terme.");
            }
        }
    }
}
