-- <PERSON>ript para hacer TypeId obligatorio en la tabla Metadonnees
-- Fecha: $(date)
-- Descripción: Convierte TypeId de nullable a NOT NULL en la tabla Metadonnees

USE [Gouvernance];
GO

PRINT '🔧 CORRECCIÓN: Hacer TypeId obligatorio en Metadonnees';
PRINT '======================================================';

-- 1. Verificar si la tabla existe
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'Metadonnees' AND schema_id = SCHEMA_ID('Metadonnees'))
BEGIN
    PRINT '✅ Tabla Metadonnees encontrada';
    
    -- 2. Verificar si la columna TypeId existe
    IF EXISTS (SELECT * FROM sys.columns WHERE name = 'TypeId' AND object_id = OBJECT_ID('Metadonnees.Metadonnees'))
    BEGIN
        PRINT '✅ Columna TypeId encontrada';
        
        -- 3. Verificar si hay registros con TypeId NULL
        DECLARE @NullCount INT;
        SELECT @NullCount = COUNT(*) FROM [Metadonnees].[Metadonnees] WHERE [TypeId] IS NULL;
        
        IF @NullCount > 0
        BEGIN
            PRINT '⚠️ Encontrados ' + CAST(@NullCount AS VARCHAR(10)) + ' registros con TypeId NULL';
            PRINT '🔧 Actualizando registros con TypeId NULL al tipo "Technique" por defecto...';
            
            -- Actualizar registros NULL con el tipo "Technique" por defecto
            UPDATE [Metadonnees].[Metadonnees] 
            SET [TypeId] = '8D7D1C3A-9E0D-4F35-A8C7-506E2E20CA25' -- Technique
            WHERE [TypeId] IS NULL;
            
            PRINT '✅ Registros actualizados correctamente';
        END
        ELSE
        BEGIN
            PRINT '✅ No hay registros con TypeId NULL';
        END
        
        -- 4. Verificar si la columna ya es NOT NULL
        IF EXISTS (
            SELECT * FROM sys.columns 
            WHERE name = 'TypeId' 
            AND object_id = OBJECT_ID('Metadonnees.Metadonnees')
            AND is_nullable = 1
        )
        BEGIN
            PRINT '🔧 Cambiando TypeId de nullable a NOT NULL...';
            
            -- Cambiar la columna a NOT NULL
            ALTER TABLE [Metadonnees].[Metadonnees] 
            ALTER COLUMN [TypeId] uniqueidentifier NOT NULL;
            
            PRINT '✅ TypeId ahora es NOT NULL';
        END
        ELSE
        BEGIN
            PRINT '✅ TypeId ya es NOT NULL';
        END
        
        -- 5. Verificar si existe la foreign key constraint
        IF NOT EXISTS (
            SELECT * FROM sys.foreign_keys 
            WHERE name = 'FK_Metadonnees_TypesMetadonnees_TypeId'
        )
        BEGIN
            PRINT '🔧 Agregando foreign key constraint para TypeId...';
            
            ALTER TABLE [Metadonnees].[Metadonnees]
            ADD CONSTRAINT [FK_Metadonnees_TypesMetadonnees_TypeId] 
            FOREIGN KEY ([TypeId]) REFERENCES [Metadonnees].[TypesMetadonnees] ([Id]);
            
            PRINT '✅ Foreign key constraint agregada';
        END
        ELSE
        BEGIN
            PRINT '✅ Foreign key constraint ya existe';
        END
        
    END
    ELSE
    BEGIN
        PRINT '❌ Columna TypeId no encontrada en la tabla Metadonnees';
    END
END
ELSE
BEGIN
    PRINT '❌ Tabla Metadonnees no encontrada';
END

-- 6. Verificar el resultado final
PRINT '';
PRINT '📊 VERIFICACIÓN FINAL:';
PRINT '----------------------';

SELECT 
    c.COLUMN_NAME as 'Columna',
    c.DATA_TYPE as 'Tipo',
    c.IS_NULLABLE as 'Nullable',
    CASE WHEN fk.CONSTRAINT_NAME IS NOT NULL THEN 'Sí' ELSE 'No' END as 'Tiene_FK'
FROM INFORMATION_SCHEMA.COLUMNS c
LEFT JOIN INFORMATION_SCHEMA.KEY_COLUMN_USAGE fk 
    ON c.COLUMN_NAME = fk.COLUMN_NAME 
    AND c.TABLE_NAME = fk.TABLE_NAME 
    AND c.TABLE_SCHEMA = fk.TABLE_SCHEMA
    AND fk.CONSTRAINT_NAME LIKE 'FK_%'
WHERE c.TABLE_SCHEMA = 'Metadonnees' 
    AND c.TABLE_NAME = 'Metadonnees' 
    AND c.COLUMN_NAME = 'TypeId';

PRINT '';
PRINT '✅ Corrección completada exitosamente!';
