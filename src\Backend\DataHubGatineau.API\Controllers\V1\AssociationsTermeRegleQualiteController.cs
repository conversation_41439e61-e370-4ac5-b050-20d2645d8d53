using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Domain.Interfaces.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers.V1;

/// <summary>
/// Contrôleur pour la gestion des associations entre termes du glossaire et règles de qualité.
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
public class AssociationsTermeRegleQualiteController : ControllerBase
{
    private readonly IDepotTermeGlossaireRegleQualite _depotAssociation;
    private readonly IDepotTermeGlossaire _depotTermeGlossaire;
    private readonly IDepotRegleQualite _depotRegleQualite;
    private readonly ILogger<AssociationsTermeRegleQualiteController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance du contrôleur des associations terme-règle qualité.
    /// </summary>
    public AssociationsTermeRegleQualiteController(
        IDepotTermeGlossaireRegleQualite depotAssociation,
        IDepotTermeGlossaire depotTermeGlossaire,
        IDepotRegleQualite depotRegleQualite,
        ILogger<AssociationsTermeRegleQualiteController> logger)
    {
        _depotAssociation = depotAssociation;
        _depotTermeGlossaire = depotTermeGlossaire;
        _depotRegleQualite = depotRegleQualite;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les associations avec leurs entités liées.
    /// </summary>
    /// <returns>Liste des associations.</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirToutesLesAssociations()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirTousAvecEntitesLieesAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations terme-règle qualité");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient une association par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association trouvée.</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TermeGlossaireRegleQualite), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TermeGlossaireRegleQualite>> ObtenirAssociationParId(Guid id)
    {
        try
        {
            var association = await _depotAssociation.ObtenirParIdAvecEntitesLieesAsync(id);
            if (association == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            return Ok(association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Liste des associations pour le terme.</returns>
    [HttpGet("terme/{termeGlossaireId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsParTerme(Guid termeGlossaireId)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParTermeGlossaireAsync(termeGlossaireId);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations pour le terme {TermeId}", termeGlossaireId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations pour une règle de qualité.
    /// </summary>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>Liste des associations pour la règle.</returns>
    [HttpGet("regle-qualite/{regleQualiteId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsParRegleQualite(Guid regleQualiteId)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParRegleQualiteAsync(regleQualiteId);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations pour la règle {RegleId}", regleQualiteId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par type de validation.
    /// </summary>
    /// <param name="typeValidation">Type de validation.</param>
    /// <returns>Liste des associations du type spécifié.</returns>
    [HttpGet("type-validation/{typeValidation}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsParTypeValidation(string typeValidation)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParTypeValidationAsync(typeValidation);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par type de validation {Type}", typeValidation);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations actives.
    /// </summary>
    /// <returns>Liste des associations actives.</returns>
    [HttpGet("actives")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsActives()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirAssociationsActivesAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations actives");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations obligatoires.
    /// </summary>
    /// <returns>Liste des associations obligatoires.</returns>
    [HttpGet("obligatoires")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsObligatoires()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirAssociationsObligatoiresAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations obligatoires");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par niveau de priorité.
    /// </summary>
    /// <param name="priorite">Niveau de priorité (1=Haute, 2=Moyenne, 3=Basse).</param>
    /// <returns>Liste des associations de la priorité spécifiée.</returns>
    [HttpGet("priorite/{priorite:int}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsParPriorite(int priorite)
    {
        try
        {
            if (priorite < 1 || priorite > 3)
            {
                return BadRequest("La priorité doit être entre 1 (Haute) et 3 (Basse)");
            }

            var associations = await _depotAssociation.ObtenirParPrioriteAsync(priorite);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par priorité {Priorite}", priorite);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations avec seuil spécifique.
    /// </summary>
    /// <returns>Liste des associations avec seuil personnalisé.</returns>
    [HttpGet("avec-seuil-specifique")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireRegleQualite>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireRegleQualite>>> ObtenirAssociationsAvecSeuilSpecifique()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirAvecSeuilSpecifiqueAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations avec seuil spécifique");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les statistiques des associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    [HttpGet("statistiques")]
    [ProducesResponseType(typeof(Dictionary<string, int>), StatusCodes.Status200OK)]
    public async Task<ActionResult<Dictionary<string, int>>> ObtenirStatistiquesAssociations()
    {
        try
        {
            var statistiques = await _depotAssociation.ObtenirStatistiquesAsync();
            return Ok(statistiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques des associations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée une nouvelle association entre un terme et une règle de qualité.
    /// </summary>
    /// <param name="association">Association à créer.</param>
    /// <returns>Association créée.</returns>
    [HttpPost]
    [ProducesResponseType(typeof(TermeGlossaireRegleQualite), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TermeGlossaireRegleQualite>> CreerAssociation([FromBody] TermeGlossaireRegleQualite association)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Vérifier si l'association existe déjà
            if (await _depotAssociation.AssociationExisteAsync(association.TermeGlossaireId, association.RegleQualiteId))
            {
                return BadRequest("Une association entre ce terme et cette règle de qualité existe déjà");
            }

            // Vérifier que le terme et la règle existent
            var terme = await _depotTermeGlossaire.ObtenirParIdAsync(association.TermeGlossaireId);
            if (terme == null)
            {
                return BadRequest($"Terme du glossaire avec l'ID {association.TermeGlossaireId} non trouvé");
            }

            var regle = await _depotRegleQualite.ObtenirParIdAsync(association.RegleQualiteId);
            if (regle == null)
            {
                return BadRequest($"Règle de qualité avec l'ID {association.RegleQualiteId} non trouvée");
            }

            association.Id = Guid.NewGuid();
            association.DateAssociation = DateTime.UtcNow;
            association.DateCreation = DateTime.UtcNow;
            association.DateModification = DateTime.UtcNow;

            await _depotAssociation.AjouterAsync(association);
            
            return CreatedAtAction(nameof(ObtenirAssociationParId), new { id = association.Id }, association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'association");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Active ou désactive une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="estActive">État d'activation.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpPatch("{id:guid}/activation")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> ChangerEtatActivationAssociation(Guid id, [FromBody] bool estActive)
    {
        try
        {
            var resultat = await _depotAssociation.ChangerEtatActivationAsync(id, estActive);
            if (!resultat)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            var message = estActive ? "Association activée avec succès" : "Association désactivée avec succès";
            return Ok(new { message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du changement d'état d'activation de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprime une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> SupprimerAssociation(Guid id)
    {
        try
        {
            var association = await _depotAssociation.ObtenirParIdAsync(id);
            if (association == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            await _depotAssociation.SupprimerAsync(association.Id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
