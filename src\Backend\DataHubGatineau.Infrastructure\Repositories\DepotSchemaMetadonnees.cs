using Microsoft.EntityFrameworkCore;
using DataHubGatineau.Domain.Entities.Metadata;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Infrastructure.Persistence;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les schémas de métadonnées.
/// </summary>
public class DepotSchemaMetadonnees : DepotBase<SchemaMetadonnees>, IDepotSchemaMetadonnees
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotSchemaMetadonnees"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotSchemaMetadonnees(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient tous les schémas de métadonnées avec leurs définitions.
    /// </summary>
    /// <returns>Une collection de schémas de métadonnées.</returns>
    public async Task<IEnumerable<SchemaMetadonnees>> ObtenirTousAvecDefinitionsAsync()
    {
        return await _context.SchemasMetadonnees
            .Include(s => s.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
            .OrderByDescending(s => s.DateModification)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient un schéma de métadonnées par son identifiant avec ses définitions.
    /// </summary>
    /// <param name="id">Identifiant du schéma.</param>
    /// <returns>Le schéma de métadonnées ou null s'il n'est pas trouvé.</returns>
    public async Task<SchemaMetadonnees?> ObtenirParIdAvecDefinitionsAsync(Guid id)
    {
        return await _context.SchemasMetadonnees
            .Include(s => s.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
            .FirstOrDefaultAsync(s => s.Id == id);
    }

    /// <summary>
    /// Obtient les schémas de métadonnées par type d'actif.
    /// </summary>
    /// <param name="typeActif">Type d'actif.</param>
    /// <returns>Une collection de schémas de métadonnées pour le type d'actif spécifié.</returns>
    public async Task<IEnumerable<SchemaMetadonnees>> ObtenirParTypeActifAsync(string typeActif)
    {
        return await _context.SchemasMetadonnees
            .Include(s => s.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
            .Where(s => s.TypeActif.ToLower() == typeActif.ToLower())
            .OrderByDescending(s => s.DateModification)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les schémas de métadonnées actifs.
    /// </summary>
    /// <returns>Une collection de schémas de métadonnées actifs.</returns>
    public async Task<IEnumerable<SchemaMetadonnees>> ObtenirActifsAsync()
    {
        return await _context.SchemasMetadonnees
            .Include(s => s.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
            .Where(s => s.EstActif)
            .OrderBy(s => s.TypeActif)
            .ThenByDescending(s => s.DateModification)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient le schéma de métadonnées actif pour un type d'actif.
    /// </summary>
    /// <param name="typeActif">Type d'actif.</param>
    /// <returns>Le schéma de métadonnées actif pour le type d'actif spécifié, ou null si non trouvé.</returns>
    public async Task<SchemaMetadonnees?> ObtenirActifParTypeActifAsync(string typeActif)
    {
        return await _context.SchemasMetadonnees
            .Include(s => s.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
            .FirstOrDefaultAsync(s => s.TypeActif.ToLower() == typeActif.ToLower() && s.EstActif);
    }

    /// <summary>
    /// Active un schéma de métadonnées et désactive les autres schémas pour le même type d'actif.
    /// </summary>
    /// <param name="schemaId">Identifiant du schéma à activer.</param>
    /// <returns>Le schéma activé.</returns>
    public async Task<SchemaMetadonnees> ActiverSchemaAsync(Guid schemaId)
    {
        var schema = await ObtenirParIdAsync(schemaId);
        if (schema == null)
        {
            throw new InvalidOperationException($"Schéma avec l'ID {schemaId} non trouvé.");
        }

        // Désactiver tous les autres schémas du même type d'actif
        var autresSchemas = await _context.SchemasMetadonnees
            .Where(s => s.TypeActif == schema.TypeActif && s.Id != schemaId && s.EstActif)
            .ToListAsync();

        foreach (var autreSchema in autresSchemas)
        {
            autreSchema.Desactiver();
        }

        // Activer le schéma sélectionné
        schema.Activer();

        await _context.SaveChangesAsync();
        return schema;
    }

    /// <summary>
    /// Crée une nouvelle version d'un schéma de métadonnées.
    /// </summary>
    /// <param name="schemaId">Identifiant du schéma à versionner.</param>
    /// <param name="nouvelleVersion">Nouvelle version.</param>
    /// <returns>La nouvelle version du schéma.</returns>
    public async Task<SchemaMetadonnees> CreerNouvelleVersionAsync(Guid schemaId, string nouvelleVersion)
    {
        var schemaOriginal = await ObtenirParIdAvecDefinitionsAsync(schemaId);
        if (schemaOriginal == null)
        {
            throw new InvalidOperationException($"Schéma avec l'ID {schemaId} non trouvé.");
        }

        // Vérifier que la nouvelle version n'existe pas déjà
        var versionExistante = await _context.SchemasMetadonnees
            .FirstOrDefaultAsync(s => s.TypeActif == schemaOriginal.TypeActif && s.Version == nouvelleVersion);

        if (versionExistante != null)
        {
            throw new InvalidOperationException($"Une version {nouvelleVersion} existe déjà pour le type d'actif {schemaOriginal.TypeActif}.");
        }

        // Créer la nouvelle version
        var nouveauSchema = schemaOriginal.CreerNouvelleVersion(nouvelleVersion);

        // Ajouter à la base de données
        await AjouterAsync(nouveauSchema);
        await _context.SaveChangesAsync();

        return nouveauSchema;
    }

    /// <summary>
    /// Vérifie si un schéma peut être supprimé.
    /// </summary>
    /// <param name="schemaId">Identifiant du schéma.</param>
    /// <returns>True si le schéma peut être supprimé, false sinon.</returns>
    public async Task<bool> PeutEtreSupprime(Guid schemaId)
    {
        var schema = await ObtenirParIdAsync(schemaId);
        return schema?.PeutEtreSupprime() ?? false;
    }

    /// <summary>
    /// Obtient les types d'actifs uniques ayant des schémas.
    /// </summary>
    /// <returns>Une collection de types d'actifs.</returns>
    public async Task<IEnumerable<string>> ObtenirTypesActifsAsync()
    {
        return await _context.SchemasMetadonnees
            .Select(s => s.TypeActif)
            .Distinct()
            .OrderBy(t => t)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les statistiques des schémas de métadonnées.
    /// </summary>
    /// <returns>Un dictionnaire contenant les statistiques.</returns>
    public async Task<Dictionary<string, int>> ObtenirStatistiquesAsync()
    {
        var totalSchemas = await _context.SchemasMetadonnees.CountAsync();
        var schemasActifs = await _context.SchemasMetadonnees.CountAsync(s => s.EstActif);
        var typesActifs = await _context.SchemasMetadonnees.Select(s => s.TypeActif).Distinct().CountAsync();
        var totalDefinitions = await _context.DefinitionsMetadonnees.CountAsync();

        return new Dictionary<string, int>
        {
            ["TotalSchemas"] = totalSchemas,
            ["SchemasActifs"] = schemasActifs,
            ["TypesActifs"] = typesActifs,
            ["TotalDefinitions"] = totalDefinitions
        };
    }
}
