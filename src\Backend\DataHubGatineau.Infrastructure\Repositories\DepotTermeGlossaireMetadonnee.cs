using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces.Repositories;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les associations entre termes du glossaire et métadonnées.
/// </summary>
public class DepotTermeGlossaireMetadonnee : DepotBase<TermeGlossaireMetadonnee>, IDepotTermeGlossaireMetadonnee
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotTermeGlossaireMetadonnee"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotTermeGlossaireMetadonnee(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient toutes les associations avec les entités liées.
    /// </summary>
    /// <returns>Collection d'associations avec les entités liées.</returns>
    public async Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirTousAvecEntitesLieesAsync()
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.TermeGlossaire)
            .Include(tgm => tgm.Metadonnee)
            .OrderBy(tgm => tgm.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient une association par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association avec les entités liées si trouvée.</returns>
    public async Task<TermeGlossaireMetadonnee?> ObtenirParIdAvecEntitesLieesAsync(Guid id)
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.TermeGlossaire)
            .Include(tgm => tgm.Metadonnee)
            .FirstOrDefaultAsync(tgm => tgm.Id == id);
    }

    /// <summary>
    /// Obtient toutes les associations pour un terme du glossaire spécifique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Collection d'associations pour le terme spécifié.</returns>
    public async Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirParTermeGlossaireAsync(Guid termeGlossaireId)
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.Metadonnee)
            .Where(tgm => tgm.TermeGlossaireId == termeGlossaireId)
            .OrderBy(tgm => tgm.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient toutes les associations pour une métadonnée spécifique.
    /// </summary>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>Collection d'associations pour la métadonnée spécifiée.</returns>
    public async Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirParMetadonneeAsync(Guid metadonneeId)
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.TermeGlossaire)
            .Where(tgm => tgm.MetadonneeId == metadonneeId)
            .OrderBy(tgm => tgm.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par type d'association.
    /// </summary>
    /// <param name="typeAssociation">Type d'association.</param>
    /// <returns>Collection d'associations du type spécifié.</returns>
    public async Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirParTypeAssociationAsync(string typeAssociation)
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.TermeGlossaire)
            .Include(tgm => tgm.Metadonnee)
            .Where(tgm => tgm.TypeAssociation == typeAssociation)
            .OrderBy(tgm => tgm.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations validées.
    /// </summary>
    /// <returns>Collection d'associations validées.</returns>
    public async Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirAssociationsValideesAsync()
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.TermeGlossaire)
            .Include(tgm => tgm.Metadonnee)
            .Where(tgm => tgm.EstValidee)
            .OrderBy(tgm => tgm.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations suggérées avec un score de confiance minimum.
    /// </summary>
    /// <param name="scoreMinimum">Score de confiance minimum.</param>
    /// <returns>Collection d'associations suggérées.</returns>
    public async Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirSuggestionsAsync(decimal scoreMinimum = 0.7m)
    {
        return await _context.TermesGlossaireMetadonnees
            .Include(tgm => tgm.TermeGlossaire)
            .Include(tgm => tgm.Metadonnee)
            .Where(tgm => tgm.TypeAssociation == "Suggere" && 
                         tgm.ConfianceScore.HasValue && 
                         tgm.ConfianceScore >= scoreMinimum)
            .OrderByDescending(tgm => tgm.ConfianceScore)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si une association existe déjà entre un terme et une métadonnée.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>True si l'association existe, false sinon.</returns>
    public async Task<bool> AssociationExisteAsync(Guid termeGlossaireId, Guid metadonneeId)
    {
        return await _context.TermesGlossaireMetadonnees
            .AnyAsync(tgm => tgm.TermeGlossaireId == termeGlossaireId && 
                            tgm.MetadonneeId == metadonneeId);
    }

    /// <summary>
    /// Obtient les statistiques d'associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    public async Task<Dictionary<string, int>> ObtenirStatistiquesAsync()
    {
        var stats = new Dictionary<string, int>();

        stats["Total"] = await _context.TermesGlossaireMetadonnees.CountAsync();
        stats["Validees"] = await _context.TermesGlossaireMetadonnees.CountAsync(tgm => tgm.EstValidee);
        stats["Manuelles"] = await _context.TermesGlossaireMetadonnees.CountAsync(tgm => tgm.TypeAssociation == "Manuel");
        stats["Automatiques"] = await _context.TermesGlossaireMetadonnees.CountAsync(tgm => tgm.TypeAssociation == "Automatique");
        stats["Suggerees"] = await _context.TermesGlossaireMetadonnees.CountAsync(tgm => tgm.TypeAssociation == "Suggere");

        return stats;
    }

    /// <summary>
    /// Supprime toutes les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    public async Task<int> SupprimerParTermeGlossaireAsync(Guid termeGlossaireId)
    {
        var associations = await _context.TermesGlossaireMetadonnees
            .Where(tgm => tgm.TermeGlossaireId == termeGlossaireId)
            .ToListAsync();

        _context.TermesGlossaireMetadonnees.RemoveRange(associations);
        await _context.SaveChangesAsync();

        return associations.Count;
    }

    /// <summary>
    /// Supprime toutes les associations pour une métadonnée.
    /// </summary>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    public async Task<int> SupprimerParMetadonneeAsync(Guid metadonneeId)
    {
        var associations = await _context.TermesGlossaireMetadonnees
            .Where(tgm => tgm.MetadonneeId == metadonneeId)
            .ToListAsync();

        _context.TermesGlossaireMetadonnees.RemoveRange(associations);
        await _context.SaveChangesAsync();

        return associations.Count;
    }
}
