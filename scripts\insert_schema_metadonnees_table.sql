-- Script pour créer un schéma de métadonnées pour le type "Table" avec la définition "Statut Document"
-- Exécution: sqlcmd -S localhost -d Gouvernance -i insert_schema_metadonnees_table.sql

USE Gouvernance;
GO

PRINT '🔧 CRÉATION DU SCHÉMA DE MÉTADONNÉES POUR LE TYPE "TABLE"';
PRINT '=======================================================';
PRINT '';

-- Vérifier si un schéma actif existe déjà pour le type "Table"
IF EXISTS (SELECT 1 FROM [Metadonnees].[SchemasMetadonnees] WHERE [TypeActif] = 'Table' AND [EstActif] = 1)
BEGIN
    PRINT '⚠️ Un schéma actif existe déjà pour le type "Table". Désactivation...';
    UPDATE [Metadonnees].[SchemasMetadonnees] 
    SET [EstActif] = 0, [DateModification] = GETDATE(), [ModifiePar] = 'Script'
    WHERE [TypeActif] = 'Table' AND [EstActif] = 1;
END

-- Créer le schéma de métadonnées pour le type "Table"
DECLARE @SchemaId UNIQUEIDENTIFIER = NEWID();

INSERT INTO [Metadonnees].[SchemasMetadonnees] (
    [Id], [Nom], [Description], [TypeActif], [Version], [EstActif], 
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    @SchemaId,
    'Schéma Standard pour Tables',
    'Schéma de métadonnées standard pour les tables de base de données, incluant les informations de statut, qualité et gouvernance.',
    'Table',
    '1.0',
    1, -- Actif
    GETDATE(),
    GETDATE(),
    'Script',
    'Script'
);

PRINT '✅ Schéma de métadonnées créé pour le type "Table"';

-- Créer les définitions de métadonnées
PRINT '';
PRINT '📋 Création des définitions de métadonnées...';

-- 1. Statut Document (ce que tu cherches!)
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Statut Document', 
    'Statut du document ou de la table dans le cycle de vie (Brouillon, En révision, Approuvé, Archivé)',
    'Liste', 1, 0, 'Brouillon', 
    '["Brouillon", "En révision", "Approuvé", "Publié", "Archivé"]', 1,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 2. Niveau de Qualité
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Niveau de Qualité', 
    'Niveau de qualité des données dans cette table (Bronze, Argent, Or)',
    'Liste', 1, 0, 'Bronze', 
    '["Bronze", "Argent", "Or"]', 2,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 3. Criticité Métier
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Criticité Métier', 
    'Niveau de criticité de cette table pour les opérations métier',
    'Liste', 1, 0, 'Moyenne', 
    '["Faible", "Moyenne", "Élevée", "Critique"]', 3,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 4. Fréquence de Mise à Jour
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Fréquence de Mise à Jour', 
    'Fréquence à laquelle les données de cette table sont mises à jour',
    'Liste', 0, 0, 'Quotidienne', 
    '["Temps réel", "Horaire", "Quotidienne", "Hebdomadaire", "Mensuelle", "Annuelle", "Ad-hoc"]', 4,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 5. Responsable Données
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Responsable Données', 
    'Personne ou équipe responsable de la qualité et de la gouvernance de ces données',
    'Texte', 1, 0, '', 
    '', 5,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 6. Classification Sensibilité
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Classification Sensibilité', 
    'Niveau de sensibilité des données selon les politiques de sécurité',
    'Liste', 1, 0, 'Public', 
    '["Public", "Interne", "Confidentiel", "Restreint"]', 6,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 7. Durée de Rétention
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Durée de Rétention', 
    'Durée pendant laquelle les données doivent être conservées (en années)',
    'Nombre', 0, 0, '7', 
    '', 7,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

-- 8. Source Système
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id], [SchemaMetadonneesId], [Nom], [Description], [TypeDonnee], 
    [EstObligatoire], [EstCalcule], [ValeurParDefaut], [ReglesValidation], [Ordre],
    [DateCreation], [DateModification], [CreePar], [ModifiePar]
)
VALUES (
    NEWID(), @SchemaId, 'Source Système', 
    'Système source d''où proviennent ces données',
    'Texte', 0, 0, '', 
    '', 8,
    GETDATE(), GETDATE(), 'Script', 'Script'
);

PRINT '✅ 8 définitions de métadonnées créées';

-- Vérifier les résultats
PRINT '';
PRINT '📊 RÉSULTATS:';
PRINT '=============';

SELECT 
    s.Nom AS 'Schéma',
    s.TypeActif AS 'Type Actif',
    s.EstActif AS 'Actif',
    COUNT(d.Id) AS 'Nb Définitions'
FROM [Metadonnees].[SchemasMetadonnees] s
LEFT JOIN [Metadonnees].[DefinitionsMetadonnees] d ON s.Id = d.SchemaMetadonneesId
WHERE s.TypeActif = 'Table'
GROUP BY s.Id, s.Nom, s.TypeActif, s.EstActif;

PRINT '';
PRINT '📋 Définitions créées:';
SELECT 
    d.Ordre AS 'Ordre',
    d.Nom AS 'Nom Définition',
    d.TypeDonnee AS 'Type',
    d.EstObligatoire AS 'Obligatoire',
    d.ValeurParDefaut AS 'Valeur par Défaut'
FROM [Metadonnees].[DefinitionsMetadonnees] d
INNER JOIN [Metadonnees].[SchemasMetadonnees] s ON d.SchemaMetadonneesId = s.Id
WHERE s.TypeActif = 'Table' AND s.EstActif = 1
ORDER BY d.Ordre;

PRINT '';
PRINT '🎉 Schéma de métadonnées pour "Table" créé avec succès!';
PRINT '   Maintenant, la définition "Statut Document" devrait apparaître';
PRINT '   dans la page d''édition des actifs de type "Table".';
