using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Domain.Entites;

/// <summary>
/// Représente un domaine d'affaires dans le système.
/// Un domaine d'affaires définit un secteur d'activité ou département fonctionnel
/// où les termes du glossaire sont utilisés (ex: Finance, Marketing, Opérations).
/// </summary>
public class DomaineAffaires : EntiteBase
{
    /// <summary>
    /// Obtient ou définit le nom du domaine d'affaires.
    /// </summary>
    [Required]
    [StringLength(100)]
    public string Nom { get; set; } = string.Empty;

    /// <summary>
    /// Obtient ou définit la description du domaine d'affaires.
    /// </summary>
    [StringLength(500)]
    public string? Description { get; set; }

    /// <summary>
    /// Obtient ou définit le code court du domaine d'affaires.
    /// Utilisé pour l'identification rapide et les intégrations.
    /// </summary>
    [StringLength(10)]
    public string? Code { get; set; }

    /// <summary>
    /// Obtient ou définit si ce domaine d'affaires est actif.
    /// </summary>
    public bool EstActif { get; set; } = true;

    /// <summary>
    /// Obtient ou définit l'ordre d'affichage du domaine d'affaires.
    /// </summary>
    public int Ordre { get; set; } = 0;

    /// <summary>
    /// Obtient ou définit la couleur associée au domaine d'affaires (format hexadécimal).
    /// Utilisée pour l'affichage dans l'interface utilisateur.
    /// </summary>
    [StringLength(7)]
    public string? Couleur { get; set; }

    /// <summary>
    /// Navigation vers les termes du glossaire associés à ce domaine d'affaires.
    /// </summary>
    public virtual ICollection<TermeGlossaire> TermesGlossaire { get; set; } = new List<TermeGlossaire>();
}
