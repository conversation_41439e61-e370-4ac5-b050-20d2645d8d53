﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class ActualizarConfiguracionDomaineAffaires : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Metadonnees_CategoriesMetadonnees_CategorieId",
                schema: "Metadonnees",
                table: "Metadonnees");

            migrationBuilder.DropForeignKey(
                name: "FK_Metadonnees_TypesMetadonnees_TypeId",
                schema: "Metadonnees",
                table: "Metadonnees");

            migrationBuilder.DropColumn(
                name: "Type",
                schema: "Metadonnees",
                table: "Metadonnees");

            migrationBuilder.DropColumn(
                name: "UtilisateurCreation",
                schema: "Gouvernance",
                table: "DomainesAffaires");

            migrationBuilder.DropColumn(
                name: "UtilisateurModification",
                schema: "Gouvernance",
                table: "DomainesAffaires");

            migrationBuilder.AlterColumn<DateTime>(
                name: "DateModification",
                schema: "Gouvernance",
                table: "DomainesAffaires",
                type: "datetime2",
                nullable: false,
                defaultValue: new DateTime(1, 1, 1, 0, 0, 0, 0, DateTimeKind.Unspecified),
                oldClrType: typeof(DateTime),
                oldType: "datetime2",
                oldNullable: true);

            migrationBuilder.AddColumn<string>(
                name: "CreePar",
                schema: "Gouvernance",
                table: "DomainesAffaires",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddColumn<string>(
                name: "ModifiePar",
                schema: "Gouvernance",
                table: "DomainesAffaires",
                type: "nvarchar(max)",
                nullable: false,
                defaultValue: "");

            migrationBuilder.AddForeignKey(
                name: "FK_Metadonnees_CategoriesMetadonnees_CategorieId",
                schema: "Metadonnees",
                table: "Metadonnees",
                column: "CategorieId",
                principalSchema: "Metadonnees",
                principalTable: "CategoriesMetadonnees",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);

            migrationBuilder.AddForeignKey(
                name: "FK_Metadonnees_TypesMetadonnees_TypeId",
                schema: "Metadonnees",
                table: "Metadonnees",
                column: "TypeId",
                principalSchema: "Metadonnees",
                principalTable: "TypesMetadonnees",
                principalColumn: "Id",
                onDelete: ReferentialAction.SetNull);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_Metadonnees_CategoriesMetadonnees_CategorieId",
                schema: "Metadonnees",
                table: "Metadonnees");

            migrationBuilder.DropForeignKey(
                name: "FK_Metadonnees_TypesMetadonnees_TypeId",
                schema: "Metadonnees",
                table: "Metadonnees");

            migrationBuilder.DropColumn(
                name: "CreePar",
                schema: "Gouvernance",
                table: "DomainesAffaires");

            migrationBuilder.DropColumn(
                name: "ModifiePar",
                schema: "Gouvernance",
                table: "DomainesAffaires");

            migrationBuilder.AddColumn<int>(
                name: "Type",
                schema: "Metadonnees",
                table: "Metadonnees",
                type: "int",
                nullable: true);

            migrationBuilder.AlterColumn<DateTime>(
                name: "DateModification",
                schema: "Gouvernance",
                table: "DomainesAffaires",
                type: "datetime2",
                nullable: true,
                oldClrType: typeof(DateTime),
                oldType: "datetime2");

            migrationBuilder.AddColumn<string>(
                name: "UtilisateurCreation",
                schema: "Gouvernance",
                table: "DomainesAffaires",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "UtilisateurModification",
                schema: "Gouvernance",
                table: "DomainesAffaires",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_Metadonnees_CategoriesMetadonnees_CategorieId",
                schema: "Metadonnees",
                table: "Metadonnees",
                column: "CategorieId",
                principalSchema: "Metadonnees",
                principalTable: "CategoriesMetadonnees",
                principalColumn: "Id");

            migrationBuilder.AddForeignKey(
                name: "FK_Metadonnees_TypesMetadonnees_TypeId",
                schema: "Metadonnees",
                table: "Metadonnees",
                column: "TypeId",
                principalSchema: "Metadonnees",
                principalTable: "TypesMetadonnees",
                principalColumn: "Id");
        }
    }
}
