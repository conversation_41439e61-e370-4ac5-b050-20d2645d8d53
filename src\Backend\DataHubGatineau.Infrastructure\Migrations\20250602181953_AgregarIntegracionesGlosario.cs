﻿using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class AgregarIntegracionesGlosario : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "Politiques",
                schema: "Gouvernance",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Code = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Titre = table.Column<string>(type: "nvarchar(200)", maxLength: 200, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Contenu = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    Statut = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    NiveauApplication = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false),
                    Categorie = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    MotsCles = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Version = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "1.0"),
                    PolitiqueParenteId = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    DateEntreeVigueur = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateExpiration = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateProchaineRevision = table.Column<DateTime>(type: "datetime2", nullable: true),
                    Proprietaire = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Approbateur = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateApprobation = table.Column<DateTime>(type: "datetime2", nullable: true),
                    EstActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreePar = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ModifiePar = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_Politiques", x => x.Id);
                    table.ForeignKey(
                        name: "FK_Politiques_Politiques_PolitiqueParenteId",
                        column: x => x.PolitiqueParenteId,
                        principalSchema: "Gouvernance",
                        principalTable: "Politiques",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Restrict);
                });

            migrationBuilder.CreateTable(
                name: "TermesGlossaireMetadonnees",
                schema: "Gouvernance",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TermeGlossaireId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    MetadonneeId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TypeAssociation = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "Manuel"),
                    UtilisateurAssociation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateAssociation = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    ConfianceScore = table.Column<decimal>(type: "decimal(3,2)", precision: 3, scale: 2, nullable: true),
                    EstValidee = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Commentaires = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreePar = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ModifiePar = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TermesGlossaireMetadonnees", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TermesGlossaireMetadonnees_Metadonnees_MetadonneeId",
                        column: x => x.MetadonneeId,
                        principalSchema: "Metadonnees",
                        principalTable: "Metadonnees",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TermesGlossaireMetadonnees_TermesGlossaire_TermeGlossaireId",
                        column: x => x.TermeGlossaireId,
                        principalSchema: "Gouvernance",
                        principalTable: "TermesGlossaire",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TermesGlossaireReglesQualite",
                schema: "Gouvernance",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TermeGlossaireId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    RegleQualiteId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TypeValidation = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "Conformite"),
                    UtilisateurAssociation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateAssociation = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    EstObligatoire = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Priorite = table.Column<int>(type: "int", nullable: false, defaultValue: 3),
                    SeuilSpecifique = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    EstActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreePar = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ModifiePar = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TermesGlossaireReglesQualite", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TermesGlossaireReglesQualite_ReglesQualite_RegleQualiteId",
                        column: x => x.RegleQualiteId,
                        principalSchema: "QualiteDonnees",
                        principalTable: "ReglesQualite",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TermesGlossaireReglesQualite_TermesGlossaire_TermeGlossaireId",
                        column: x => x.TermeGlossaireId,
                        principalSchema: "Gouvernance",
                        principalTable: "TermesGlossaire",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "TermesGlossairePolitiques",
                schema: "Gouvernance",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TermeGlossaireId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    PolitiqueId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    TypeApplication = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: false, defaultValue: "Classification"),
                    UtilisateurAssociation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    DateAssociation = table.Column<DateTime>(type: "datetime2", nullable: false, defaultValueSql: "GETUTCDATE()"),
                    NiveauImpact = table.Column<string>(type: "nvarchar(20)", maxLength: 20, nullable: false, defaultValue: "Moyen"),
                    EstObligatoire = table.Column<bool>(type: "bit", nullable: false, defaultValue: false),
                    Priorite = table.Column<int>(type: "int", nullable: false, defaultValue: 3),
                    DateEntreeVigueurSpecifique = table.Column<DateTime>(type: "datetime2", nullable: true),
                    DateExpirationSpecifique = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ParametresSpecifiques = table.Column<string>(type: "nvarchar(1000)", maxLength: 1000, nullable: true),
                    Commentaires = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    EstActive = table.Column<bool>(type: "bit", nullable: false, defaultValue: true),
                    StatutConformite = table.Column<string>(type: "nvarchar(50)", maxLength: 50, nullable: true),
                    DateDerniereEvaluation = table.Column<DateTime>(type: "datetime2", nullable: true),
                    ScoreConformite = table.Column<decimal>(type: "decimal(5,2)", precision: 5, scale: 2, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: false),
                    CreePar = table.Column<string>(type: "nvarchar(max)", nullable: false),
                    ModifiePar = table.Column<string>(type: "nvarchar(max)", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_TermesGlossairePolitiques", x => x.Id);
                    table.ForeignKey(
                        name: "FK_TermesGlossairePolitiques_Politiques_PolitiqueId",
                        column: x => x.PolitiqueId,
                        principalSchema: "Gouvernance",
                        principalTable: "Politiques",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_TermesGlossairePolitiques_TermesGlossaire_TermeGlossaireId",
                        column: x => x.TermeGlossaireId,
                        principalSchema: "Gouvernance",
                        principalTable: "TermesGlossaire",
                        principalColumn: "Id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_Categorie",
                schema: "Gouvernance",
                table: "Politiques",
                column: "Categorie");

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_DateEntreeVigueur",
                schema: "Gouvernance",
                table: "Politiques",
                column: "DateEntreeVigueur");

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_DateExpiration",
                schema: "Gouvernance",
                table: "Politiques",
                column: "DateExpiration");

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_EstActive",
                schema: "Gouvernance",
                table: "Politiques",
                column: "EstActive");

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_NiveauApplication",
                schema: "Gouvernance",
                table: "Politiques",
                column: "NiveauApplication");

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_PolitiqueParenteId",
                schema: "Gouvernance",
                table: "Politiques",
                column: "PolitiqueParenteId");

            migrationBuilder.CreateIndex(
                name: "IX_Politiques_Statut",
                schema: "Gouvernance",
                table: "Politiques",
                column: "Statut");

            migrationBuilder.CreateIndex(
                name: "UX_Politiques_Code",
                schema: "Gouvernance",
                table: "Politiques",
                column: "Code",
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireMetadonnees_DateAssociation",
                schema: "Gouvernance",
                table: "TermesGlossaireMetadonnees",
                column: "DateAssociation");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireMetadonnees_MetadonneeId",
                schema: "Gouvernance",
                table: "TermesGlossaireMetadonnees",
                column: "MetadonneeId");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireMetadonnees_TermeGlossaireId",
                schema: "Gouvernance",
                table: "TermesGlossaireMetadonnees",
                column: "TermeGlossaireId");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireMetadonnees_TypeAssociation",
                schema: "Gouvernance",
                table: "TermesGlossaireMetadonnees",
                column: "TypeAssociation");

            migrationBuilder.CreateIndex(
                name: "UX_TermesGlossaireMetadonnees_TermeMetadonnee",
                schema: "Gouvernance",
                table: "TermesGlossaireMetadonnees",
                columns: new[] { "TermeGlossaireId", "MetadonneeId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_EstActive",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "EstActive");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_EstObligatoire",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "EstObligatoire");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_NiveauImpact",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "NiveauImpact");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_PolitiqueId",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "PolitiqueId");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_StatutConformite",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "StatutConformite");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_TermeGlossaireId",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "TermeGlossaireId");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossairePolitiques_TypeApplication",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                column: "TypeApplication");

            migrationBuilder.CreateIndex(
                name: "UX_TermesGlossairePolitiques_TermePolitique",
                schema: "Gouvernance",
                table: "TermesGlossairePolitiques",
                columns: new[] { "TermeGlossaireId", "PolitiqueId" },
                unique: true);

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireReglesQualite_EstActive",
                schema: "Gouvernance",
                table: "TermesGlossaireReglesQualite",
                column: "EstActive");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireReglesQualite_EstObligatoire",
                schema: "Gouvernance",
                table: "TermesGlossaireReglesQualite",
                column: "EstObligatoire");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireReglesQualite_RegleQualiteId",
                schema: "Gouvernance",
                table: "TermesGlossaireReglesQualite",
                column: "RegleQualiteId");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireReglesQualite_TermeGlossaireId",
                schema: "Gouvernance",
                table: "TermesGlossaireReglesQualite",
                column: "TermeGlossaireId");

            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaireReglesQualite_TypeValidation",
                schema: "Gouvernance",
                table: "TermesGlossaireReglesQualite",
                column: "TypeValidation");

            migrationBuilder.CreateIndex(
                name: "UX_TermesGlossaireReglesQualite_TermeRegle",
                schema: "Gouvernance",
                table: "TermesGlossaireReglesQualite",
                columns: new[] { "TermeGlossaireId", "RegleQualiteId" },
                unique: true);
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "TermesGlossaireMetadonnees",
                schema: "Gouvernance");

            migrationBuilder.DropTable(
                name: "TermesGlossairePolitiques",
                schema: "Gouvernance");

            migrationBuilder.DropTable(
                name: "TermesGlossaireReglesQualite",
                schema: "Gouvernance");

            migrationBuilder.DropTable(
                name: "Politiques",
                schema: "Gouvernance");
        }
    }
}
