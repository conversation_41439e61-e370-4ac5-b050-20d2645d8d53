@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@using DataHubGatineau.Web.Models.Metadata
@using System.Text.Json
@using System.Text.Json.Serialization
@inject IMetadonneeService MetadonneeService
@inject ITypeMetadonneeService TypeMetadonneeService
@inject ICategorieMetadonneeService CategorieMetadonneeService
@inject ITermeGlossaireService TermeGlossaireService
@inject IExceptionHandlingService ExceptionHandlingService
@inject IActifDonneesService ActifDonneesService
@inject ISchemaMetadonneesService SchemaMetadonneesService

<div class="row">
    <div class="col-md-6">
        <h5>Métadonnées techniques</h5>
        <div class="mb-3">
            <label for="cheminAcces" class="form-label">Chemin d'accès</label>
            <input type="text" class="form-control" id="cheminAcces" @bind="ActifDonnees.CheminAcces" @bind:after="NotifyChange" />
        </div>
        <div class="mb-3">
            <label for="frequenceMiseAJour" class="form-label">Fréquence de mise à jour</label>
            <select id="frequenceMiseAJour" class="form-select" @bind="FrequenceMiseAJourId" @bind:after="UpdateFrequenceMiseAJour">
                <option value="">Sélectionner une fréquence</option>
                @if (FrequencesMiseAJour != null)
                {
                    @foreach (var frequence in FrequencesMiseAJour)
                    {
                        <option value="@frequence.Id">@frequence.Nom</option>
                    }
                }
            </select>
        </div>
        <div class="mb-3">
            <label for="dateDerniereMiseAJour" class="form-label">Date de dernière mise à jour</label>
            <input type="date" class="form-control" id="dateDerniereMiseAJour"
                   @bind="DateDerniereMiseAJour" @bind:after="UpdateDateDerniereMiseAJour" />
        </div>

        <!-- Métadonnées du schéma actif -->
        @if (SchemaActif != null && SchemaActif.DefinitionsMetadonnees != null && SchemaActif.DefinitionsMetadonnees.Any())
        {
            <h5 class="mt-4">Métadonnées du schéma (@SchemaActif.Nom)</h5>
            <div class="card">
                <div class="card-body">
                    @foreach (var definition in SchemaActif.DefinitionsMetadonnees.OrderBy(d => d.Ordre))
                    {
                        <div class="mb-3">
                            <label class="form-label">
                                @definition.Nom
                                @if (definition.EstObligatoire)
                                {
                                    <span class="text-danger">*</span>
                                }
                            </label>
                            @if (!string.IsNullOrEmpty(definition.Description))
                            {
                                <small class="form-text text-muted d-block">@definition.Description</small>
                            }

                            @if (definition.TypeDonnee == "Liste")
                            {
                                <!-- Campo de lista con opciones -->
                                <select class="form-select"
                                        value="@ObtenirValeurMetadonneeSchema(definition.Nom)"
                                        @onchange="@(e => ActualizarMetadonneeSchema(definition.Nom, e.Value?.ToString() ?? string.Empty))">
                                    <option value="">-- Sélectionner une option --</option>
                                    @if (!string.IsNullOrEmpty(definition.ReglesValidation))
                                    {
                                        @try
                                        {
                                            // Intentar deserializar como objeto con propiedad "options"
                                            var optionsWrapper = JsonSerializer.Deserialize<OptionsWrapper>(definition.ReglesValidation);
                                            @if (optionsWrapper?.Options != null)
                                            {
                                                @foreach (var option in optionsWrapper.Options)
                                                {
                                                    <option value="@option">@option</option>
                                                }
                                            }
                                            else
                                            {
                                                // Fallback: intentar deserializar como lista directa
                                                var optionsList = JsonSerializer.Deserialize<List<string>>(definition.ReglesValidation);
                                                @if (optionsList != null)
                                                {
                                                    @foreach (var option in optionsList)
                                                    {
                                                        <option value="@option">@option</option>
                                                    }
                                                }
                                            }
                                        }
                                        catch (Exception ex)
                                        {
                                            <option value="" disabled>Erreur de configuration: @ex.Message</option>
                                        }
                                    }
                                </select>
                            }
                            else if (definition.TypeDonnee == "Date")
                            {
                                <!-- Campo de fecha -->
                                <input type="date" class="form-control"
                                       value="@ObtenirValeurMetadonneeSchema(definition.Nom)"
                                       @onchange="@(e => ActualizarMetadonneeSchema(definition.Nom, e.Value?.ToString() ?? string.Empty))" />
                            }
                            else if (definition.TypeDonnee == "Nombre")
                            {
                                <!-- Campo numérico -->
                                <input type="number" class="form-control"
                                       value="@ObtenirValeurMetadonneeSchema(definition.Nom)"
                                       @onchange="@(e => ActualizarMetadonneeSchema(definition.Nom, e.Value?.ToString() ?? string.Empty))" />
                            }
                            else
                            {
                                <!-- Campo de texto por defecto -->
                                <input type="text" class="form-control"
                                       value="@ObtenirValeurMetadonneeSchema(definition.Nom)"
                                       @onchange="@(e => ActualizarMetadonneeSchema(definition.Nom, e.Value?.ToString() ?? string.Empty))" />
                            }
                        </div>
                    }
                </div>
            </div>
        }

        <h5 class="mt-4">Métadonnées personnalisées</h5>
        <div class="mb-3">
            <div class="input-group">
                <input type="text" class="form-control" placeholder="Nom" @bind="NouvelleMetadonneeNom" />
                <input type="text" class="form-control" placeholder="Valeur" @bind="NouvelleMetadonneeValeur" />
                <select class="form-select" style="max-width: 150px;" @bind="SelectedTypeMetadonneeId" @bind:after="LoadCategoriesMetadonnees">
                    <option value="">Sélectionner un type</option>
                    @foreach (var type in TypesMetadonnees)
                    {
                        <option value="@type.Id">@type.Nom</option>
                    }
                </select>
                <select class="form-select" style="max-width: 150px;" @bind="SelectedCategorieMetadonneeId">
                    <option value="">Sélectionner une catégorie</option>
                    @foreach (var categorie in CategoriesMetadonnees)
                    {
                        <option value="@categorie.Id">@categorie.Nom</option>
                    }
                </select>
                <button class="btn btn-outline-primary" @onclick="AjouterMetadonnee"
                        disabled="@(string.IsNullOrEmpty(NouvelleMetadonneeNom) || string.IsNullOrEmpty(NouvelleMetadonneeValeur) || !SelectedTypeMetadonneeId.HasValue || SelectedCategorieMetadonneeId == Guid.Empty)">
                    <i class="bi bi-plus"></i>
                </button>
            </div>
        </div>
        @if (Metadonnees.Any())
        {
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Valeur</th>
                            <th>Type / Catégorie</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var metadonnee in Metadonnees)
                        {
                            <tr>
                                <td>@metadonnee.Nom</td>
                                <td>@metadonnee.Valeur</td>
                                <td>
                                    <span class="badge bg-secondary">@(metadonnee.TypeMetadonnee?.Nom ?? "Non spécifié")</span>
                                    @if (metadonnee.CategorieMetadonnee != null)
                                    {
                                        <span class="badge bg-info ms-1">@metadonnee.CategorieMetadonnee.Nom</span>
                                    }
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-danger" @onclick="() => SupprimerMetadonnee(metadonnee.Id)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="alert alert-info">
                Aucune métadonnée personnalisée.
            </div>
        }
    </div>
    <div class="col-md-6">
        <h5>Termes du glossaire associés</h5>
        <div class="mb-3">
            <div class="input-group">
                <select class="form-select" @bind="SelectedTermeId">
                    <option value="">Sélectionner un terme</option>
                    @foreach (var terme in TermesDisponibles)
                    {
                        <option value="@terme.Id">@terme.Nom</option>
                    }
                </select>
                <button class="btn btn-outline-primary" @onclick="AssocierTerme"
                        disabled="@(string.IsNullOrEmpty(SelectedTermeId.ToString()) || SelectedTermeId == Guid.Empty)">
                    <i class="bi bi-plus"></i>
                </button>
            </div>
        </div>
        @if (TermesAssocies.Any())
        {
            <div class="table-responsive">
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Nom</th>
                            <th>Domaine</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        @foreach (var terme in TermesAssocies)
                        {
                            <tr>
                                <td>@terme.Nom</td>
                                <td>@(!string.IsNullOrEmpty(terme.DomaineAffaires) ? terme.DomaineAffaires : "Non spécifié")</td>
                                <td>
                                    <button class="btn btn-sm btn-danger" @onclick="() => DissocierTerme(terme.Id)">
                                        <i class="bi bi-trash"></i>
                                    </button>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>
            </div>
        }
        else
        {
            <div class="alert alert-info">
                Aucun terme du glossaire associé.
            </div>
        }
    </div>
</div>

@code {
    [Parameter]
    public ActifDonnees ActifDonnees { get; set; } = new ActifDonnees();

    [Parameter]
    public EventCallback<ActifDonnees> OnChange { get; set; }

    [Inject]
    public IFrequenceMiseAJourService FrequenceMiseAJourService { get; set; } = default!;

    private List<Metadonnee> Metadonnees { get; set; } = new List<Metadonnee>();
    private string NouvelleMetadonneeNom { get; set; } = string.Empty;
    private string NouvelleMetadonneeValeur { get; set; } = string.Empty;
    private DateTime? DateDerniereMiseAJour { get; set; }
    private Guid FrequenceMiseAJourId { get; set; } = Guid.Empty;
    private List<FrequenceMiseAJour> FrequencesMiseAJour { get; set; } = new List<FrequenceMiseAJour>();
    private List<TypeMetadonneeItem> TypesMetadonnees { get; set; } = new List<TypeMetadonneeItem>();
    private Guid? SelectedTypeMetadonneeId { get; set; } = null;
    private List<CategorieMetadonneeItem> CategoriesMetadonnees { get; set; } = new List<CategorieMetadonneeItem>();
    private Guid SelectedCategorieMetadonneeId { get; set; } = Guid.Empty;

    private List<TermeGlossaire> TermesDisponibles { get; set; } = new List<TermeGlossaire>();
    private List<TermeGlossaire> TermesAssocies { get; set; } = new List<TermeGlossaire>();
    private Guid SelectedTermeId { get; set; } = Guid.Empty;
    private List<DomaineGouvernance> DomainesGouvernance { get; set; } = new List<DomaineGouvernance>();

    // Propiedades para el esquema de metadatos
    private SchemaMetadonnees? SchemaActif { get; set; }
    private Dictionary<string, string> ValoresMetadonneesSchema { get; set; } = new Dictionary<string, string>();

    protected override async Task OnInitializedAsync()
    {
        Console.WriteLine($"🔄 MetadonneesTab - OnInitializedAsync pour actif {ActifDonnees.Id}");
        DateDerniereMiseAJour = ActifDonnees.DateDerniereMiseAJour;
        FrequenceMiseAJourId = ActifDonnees.FrequenceMiseAJourId ?? Guid.Empty;
        await LoadFrequencesMiseAJour();
        await LoadTypesMetadonnees();
        await LoadSchemaActif();
        await LoadMetadonnees();
        await LoadTermes();
    }

    protected override async Task OnParametersSetAsync()
    {
        Console.WriteLine($"🔄 MetadonneesTab - OnParametersSetAsync pour actif {ActifDonnees.Id}");
        if (ActifDonnees.Id != Guid.Empty)
        {
            DateDerniereMiseAJour = ActifDonnees.DateDerniereMiseAJour;
            FrequenceMiseAJourId = ActifDonnees.FrequenceMiseAJourId ?? Guid.Empty;
            await LoadTypesMetadonnees();
            await LoadSchemaActif();
            await LoadMetadonnees();
            await LoadTermes();
        }
        else
        {
            Console.WriteLine($"⚠️ MetadonneesTab - OnParametersSetAsync: Actif sans ID");
        }
    }

    private async Task LoadFrequencesMiseAJour()
    {
        try
        {
            var frequences = await FrequenceMiseAJourService.ObtenirTousAsync();
            FrequencesMiseAJour = frequences.ToList();
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des fréquences de mise à jour");
        }
    }

    private async Task LoadTypesMetadonnees()
    {
        try
        {
            var types = await TypeMetadonneeService.ObtenirTousAsync();
            TypesMetadonnees = types.ToList();

            // Sélectionner le premier type par défaut s'il y en a
            if (TypesMetadonnees.Any() && !SelectedTypeMetadonneeId.HasValue)
            {
                SelectedTypeMetadonneeId = TypesMetadonnees.First().Id;
                await LoadCategoriesMetadonnees();
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des types de métadonnées");
        }
    }

    private async Task LoadCategoriesMetadonnees()
    {
        try
        {
            if (SelectedTypeMetadonneeId.HasValue)
            {
                var categories = await CategorieMetadonneeService.ObtenirParTypeAsync(SelectedTypeMetadonneeId.Value);
                CategoriesMetadonnees = categories.ToList();

                // Sélectionner la première catégorie par défaut s'il y en a
                if (CategoriesMetadonnees.Any())
                {
                    SelectedCategorieMetadonneeId = CategoriesMetadonnees.First().Id;
                }
                else
                {
                    SelectedCategorieMetadonneeId = Guid.Empty;
                    CategoriesMetadonnees = new List<CategorieMetadonneeItem>();
                }
            }
            else
            {
                SelectedCategorieMetadonneeId = Guid.Empty;
                CategoriesMetadonnees = new List<CategorieMetadonneeItem>();
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des catégories de métadonnées");
        }
    }

    private async Task LoadMetadonnees()
    {
        try
        {
            if (ActifDonnees.Id != Guid.Empty)
            {
                Console.WriteLine($"🔍 MetadonneesTab - Chargement des métadonnées pour l'actif {ActifDonnees.Id}");
                var metadonnees = await MetadonneeService.ObtenirParActifDonneesAsync(ActifDonnees.Id);
                Metadonnees = metadonnees.ToList();
                Console.WriteLine($"✅ MetadonneesTab - {Metadonnees.Count} métadonnées chargées");

                // Afficher les métadonnées chargées pour debug
                foreach (var meta in Metadonnees)
                {
                    Console.WriteLine($"   📋 Métadonnée: {meta.Nom} = {meta.Valeur}");
                }

                // Cargar valores del esquema después de cargar metadatos
                await ChargerValeursMetadonneesSchema();
            }
            else
            {
                Console.WriteLine($"⚠️ MetadonneesTab - Actif sans ID, impossible de charger les métadonnées");
                Metadonnees = new List<Metadonnee>();
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ MetadonneesTab - Erreur lors du chargement des métadonnées: {ex.Message}");
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des métadonnées");
        }
    }

    private async Task LoadTermes()
    {
        try
        {
            var termes = await TermeGlossaireService.ObtenirTousAsync();
            TermesDisponibles = termes.ToList();

            if (ActifDonnees.Id != Guid.Empty)
            {
                var termesAssocies = await TermeGlossaireService.ObtenirParActifDonneesAsync(ActifDonnees.Id);
                TermesAssocies = termesAssocies.ToList();
            }
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement des termes du glossaire");
        }
    }

    private async Task LoadSchemaActif()
    {
        try
        {
            if (ActifDonnees.TypeActifDonnees != null)
            {
                Console.WriteLine($"🔍 MetadonneesTab - Chargement du schéma actif pour le type: {ActifDonnees.TypeActifDonnees.Nom}");
                SchemaActif = await SchemaMetadonneesService.ObtenirActifParTypeActifAsync(ActifDonnees.TypeActifDonnees.Nom);

                if (SchemaActif != null)
                {
                    Console.WriteLine($"✅ MetadonneesTab - Schéma actif trouvé: {SchemaActif.Nom} avec {SchemaActif.DefinitionsMetadonnees?.Count ?? 0} définitions");
                    await ChargerValeursMetadonneesSchema();
                }
                else
                {
                    Console.WriteLine($"⚠️ MetadonneesTab - Aucun schéma actif trouvé pour le type: {ActifDonnees.TypeActifDonnees.Nom}");
                }
            }
            else
            {
                Console.WriteLine($"⚠️ MetadonneesTab - TypeActifDonnees est null, impossible de charger le schéma");
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ MetadonneesTab - Erreur lors du chargement du schéma actif: {ex.Message}");
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors du chargement du schéma de métadonnées");
        }
    }

    private async Task ChargerValeursMetadonneesSchema()
    {
        try
        {
            ValoresMetadonneesSchema.Clear();

            if (SchemaActif?.DefinitionsMetadonnees != null)
            {
                foreach (var definition in SchemaActif.DefinitionsMetadonnees)
                {
                    // Buscar si ya existe un metadato con este nombre
                    var metadonneeExistante = Metadonnees.FirstOrDefault(m => m.Nom == definition.Nom);
                    if (metadonneeExistante != null)
                    {
                        ValoresMetadonneesSchema[definition.Nom] = metadonneeExistante.Valeur ?? string.Empty;
                    }
                    else if (!string.IsNullOrEmpty(definition.ValeurParDefaut))
                    {
                        ValoresMetadonneesSchema[definition.Nom] = definition.ValeurParDefaut;
                    }
                    else
                    {
                        ValoresMetadonneesSchema[definition.Nom] = string.Empty;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ MetadonneesTab - Erreur lors du chargement des valeurs: {ex.Message}");
        }
    }

    private string ObtenirValeurMetadonneeSchema(string nom)
    {
        return ValoresMetadonneesSchema.TryGetValue(nom, out var valeur) ? valeur : string.Empty;
    }

    private async Task ActualizarMetadonneeSchema(string nom, string valeur)
    {
        try
        {
            ValoresMetadonneesSchema[nom] = valeur;

            // Buscar si ya existe un metadato con este nombre
            var metadonneeExistante = Metadonnees.FirstOrDefault(m => m.Nom == nom);

            if (metadonneeExistante != null)
            {
                // Actualizar metadato existente
                metadonneeExistante.Valeur = valeur;
                metadonneeExistante.DateModification = DateTime.Now;
                metadonneeExistante.ModifiePar = "Utilisateur";

                await MetadonneeService.MettreAJourAsync(metadonneeExistante.Id, metadonneeExistante);
                Console.WriteLine($"✅ MetadonneesTab - Métadonnée mise à jour: {nom} = {valeur}");
            }
            else if (!string.IsNullOrEmpty(valeur))
            {
                // Crear nuevo metadato
                var nouvelleMetadonnee = new Metadonnee
                {
                    Id = Guid.NewGuid(),
                    Nom = nom,
                    Valeur = valeur,
                    ActifDonneesId = ActifDonnees.Id,
                    DateCreation = DateTime.Now,
                    DateModification = DateTime.Now,
                    CreePar = "Utilisateur",
                    ModifiePar = "Utilisateur"
                };

                var result = await MetadonneeService.AjouterAsync(nouvelleMetadonnee);
                Metadonnees.Add(result);
                Console.WriteLine($"✅ MetadonneesTab - Nouvelle métadonnée créée: {nom} = {valeur}");
            }

            StateHasChanged();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"❌ MetadonneesTab - Erreur lors de la mise à jour: {ex.Message}");
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la mise à jour de la métadonnée");
        }
    }

    private async Task AjouterMetadonnee()
    {
        if (string.IsNullOrEmpty(NouvelleMetadonneeNom) || string.IsNullOrEmpty(NouvelleMetadonneeValeur))
            return;

        // Vérifier qu'un type de métadonnée est sélectionné
        if (!SelectedTypeMetadonneeId.HasValue)
        {
            await ExceptionHandlingService.HandleExceptionAsync(
                new InvalidOperationException("Veuillez sélectionner un type de métadonnée."),
                "Type de métadonnée requis");
            return;
        }

        try
        {
            // Vérifier que l'actif de données a un ID valide
            if (ActifDonnees.Id == Guid.Empty)
            {
                await ExceptionHandlingService.HandleExceptionAsync(
                    new InvalidOperationException("L'actif de données doit être enregistré avant d'ajouter des métadonnées."),
                    "Erreur lors de l'ajout de la métadonnée");
                return;
            }

            // Vérifier si l'actif existe dans la base de données
            bool actifExiste = await ActifDonneesService.ExisteAsync(ActifDonnees.Id);
            Console.WriteLine($"Vérification si l'actif existe: {actifExiste}");

            if (!actifExiste)
            {
                Console.WriteLine("L'actif n'existe pas encore dans la base de données. Tentative de sauvegarde...");

                // Obtenir le statut "Brouillon"
                var statuts = await ActifDonneesService.ObtenirStatutsAsync();
                var statutBrouillon = statuts.FirstOrDefault(s => s.Nom == "Brouillon");

                if (statutBrouillon != null)
                {
                    ActifDonnees.StatutActifDonneesId = statutBrouillon.Id;
                }

                ActifDonnees.DateModification = DateTime.Now;
                ActifDonnees.ModifiePar = "Utilisateur actuel"; // Remplacer par l'utilisateur réel

                // Sauvegarder l'actif
                var savedActif = await ActifDonneesService.AjouterAsync(ActifDonnees);
                if (savedActif != null)
                {
                    ActifDonnees.Id = savedActif.Id;
                    Console.WriteLine($"Actif sauvegardé avec ID: {ActifDonnees.Id}");

                    // Notifier le parent du changement
                    await OnChange.InvokeAsync(ActifDonnees);

                    // Afficher un message à l'utilisateur
                    await ExceptionHandlingService.HandleSuccessAsync("L'actif a été sauvegardé automatiquement pour permettre l'ajout de métadonnées.");
                }
                else
                {
                    Console.WriteLine("Échec de la sauvegarde de l'actif.");
                    await ExceptionHandlingService.HandleExceptionAsync(
                        new Exception("Impossible de sauvegarder l'actif. Veuillez remplir tous les champs obligatoires."),
                        "Erreur lors de la sauvegarde automatique");
                    return;
                }
            }

            var metadonnee = new Metadonnee
            {
                Id = Guid.NewGuid(),
                Nom = NouvelleMetadonneeNom,
                Valeur = NouvelleMetadonneeValeur,
                TypeId = SelectedTypeMetadonneeId,
                CategorieId = SelectedCategorieMetadonneeId == Guid.Empty ? null : SelectedCategorieMetadonneeId,
                ActifDonneesId = ActifDonnees.Id,
                DateCreation = DateTime.Now,
                DateModification = DateTime.Now,
                CreePar = "Utilisateur",
                ModifiePar = "Utilisateur"
            };

            Console.WriteLine($"🚀 MetadonneesTab - Ajout d'une métadonnée: {metadonnee.Nom}={metadonnee.Valeur} pour l'actif {ActifDonnees.Id}");

            try
            {
                var result = await MetadonneeService.AjouterAsync(metadonnee);
                Console.WriteLine($"✅ MetadonneesTab - Métadonnée ajoutée avec succès avec ID: {result.Id}");

                // Utiliser le résultat de l'API au lieu de l'objet local pour éviter les problèmes d'ID
                Metadonnees.Add(result);
                Console.WriteLine($"📝 MetadonneesTab - Liste locale mise à jour, total: {Metadonnees.Count} métadonnées");
                StateHasChanged();

                // Afficher un message de succès
                await ExceptionHandlingService.HandleSuccessAsync($"Métadonnée '{metadonnee.Nom}' ajoutée avec succès.");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Erreur lors de l'appel au service pour ajouter la métadonnée: {ex.Message}");
                Console.Error.WriteLine($"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    Console.Error.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    Console.Error.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
                }

                // ❌ NE PAS ajouter à la liste locale en cas d'erreur pour éviter les doublons
                // Au lieu de cela, recharger les métadonnées depuis l'API pour synchroniser
                await LoadMetadonnees();

                // Afficher un message d'erreur
                await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'ajout de la métadonnée");
            }

            // Limpiar todos los campos después de agregar exitosamente
            NouvelleMetadonneeNom = string.Empty;
            NouvelleMetadonneeValeur = string.Empty;
            SelectedTypeMetadonneeId = null;
            SelectedCategorieMetadonneeId = Guid.Empty;
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors de l'ajout de la métadonnée: {ex.Message}");
            Console.Error.WriteLine($"Stack trace: {ex.StackTrace}");

            if (ex.InnerException != null)
            {
                Console.Error.WriteLine($"Inner exception: {ex.InnerException.Message}");
                Console.Error.WriteLine($"Inner stack trace: {ex.InnerException.StackTrace}");
            }

            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'ajout de la métadonnée");
        }
    }

    private async Task SupprimerMetadonnee(Guid id)
    {
        try
        {
            await MetadonneeService.SupprimerAsync(id);
            await LoadMetadonnees();
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la suppression de la métadonnée");
        }
    }

    private async Task AssocierTerme()
    {
        if (SelectedTermeId == Guid.Empty)
            return;

        try
        {
            // Vérifier si le terme est déjà associé
            if (TermesAssocies.Any(t => t.Id == SelectedTermeId))
                return;

            // Associer le terme à l'actif de données
            // Note: Cette fonctionnalité dépend de l'implémentation du service
            await TermeGlossaireService.AssocierActifDonneesAsync(SelectedTermeId, ActifDonnees.Id);
            await LoadTermes();
            SelectedTermeId = Guid.Empty;
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de l'association du terme");
        }
    }

    private async Task DissocierTerme(Guid termeId)
    {
        try
        {
            // Dissocier le terme de l'actif de données
            // Note: Cette fonctionnalité dépend de l'implémentation du service
            await TermeGlossaireService.DissocierActifDonneesAsync(termeId, ActifDonnees.Id);
            await LoadTermes();
        }
        catch (Exception ex)
        {
            await ExceptionHandlingService.HandleExceptionAsync(ex, "Erreur lors de la dissociation du terme");
        }
    }

    private async Task UpdateDateDerniereMiseAJour()
    {
        ActifDonnees.DateDerniereMiseAJour = DateDerniereMiseAJour;
        await NotifyChange();
    }

    private async Task UpdateFrequenceMiseAJour()
    {
        // Mettre à jour l'ID de la fréquence de mise à jour dans l'actif de données
        ActifDonnees.FrequenceMiseAJourId = FrequenceMiseAJourId == Guid.Empty ? null : FrequenceMiseAJourId;

        // Mettre à jour également l'objet FrequenceMiseAJour pour la cohérence
        if (FrequenceMiseAJourId != Guid.Empty && FrequencesMiseAJour != null)
        {
            var frequence = FrequencesMiseAJour.FirstOrDefault(f => f.Id == FrequenceMiseAJourId);
            if (frequence != null)
            {
                ActifDonnees.FrequenceMiseAJour = frequence;
                Console.WriteLine($"Fréquence mise à jour: {frequence.Nom} (ID: {frequence.Id})");
            }
        }
        else
        {
            ActifDonnees.FrequenceMiseAJour = null;
        }

        // Journaliser les valeurs pour le débogage
        Console.WriteLine($"MetadonneesTab - FrequenceMiseAJourId: {FrequenceMiseAJourId}");
        Console.WriteLine($"MetadonneesTab - ActifDonnees.FrequenceMiseAJourId: {ActifDonnees.FrequenceMiseAJourId}");
        if (ActifDonnees.FrequenceMiseAJour != null)
        {
            Console.WriteLine($"MetadonneesTab - ActifDonnees.FrequenceMiseAJour.Nom: {ActifDonnees.FrequenceMiseAJour.Nom}");
        }
        else
        {
            Console.WriteLine("MetadonneesTab - ActifDonnees.FrequenceMiseAJour est null");
        }

        await NotifyChange();
    }

    private async Task NotifyChange()
    {
        await OnChange.InvokeAsync(ActifDonnees);
    }

    // Clase auxiliar para deserializar las opciones de las listas
    public class OptionsWrapper
    {
        [JsonPropertyName("options")]
        public List<string>? Options { get; set; }
    }
}

