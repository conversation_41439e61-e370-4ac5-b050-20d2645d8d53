using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Domain.Entites;

/// <summary>
/// Représente l'association entre un terme du glossaire et une métadonnée.
/// Cette entité permet d'enrichir les métadonnées avec un contexte sémantique.
/// </summary>
public class TermeGlossaireMetadonnee : EntiteBase
{
    /// <summary>
    /// Obtient ou définit l'identifiant du terme du glossaire.
    /// </summary>
    [Required]
    public Guid TermeGlossaireId { get; set; }

    /// <summary>
    /// Obtient ou définit le terme du glossaire associé.
    /// </summary>
    public virtual TermeGlossaire TermeGlossaire { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit l'identifiant de la métadonnée.
    /// </summary>
    [Required]
    public Guid MetadonneeId { get; set; }

    /// <summary>
    /// Obtient ou définit la métadonnée associée.
    /// </summary>
    public virtual Metadonnee Metadonnee { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit le type d'association entre le terme et la métadonnée.
    /// </summary>
    [Required]
    [StringLength(50)]
    public string TypeAssociation { get; set; } = "Manuel";

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé cette association.
    /// </summary>
    [StringLength(100)]
    public string? UtilisateurAssociation { get; set; }

    /// <summary>
    /// Obtient ou définit la date de création de l'association.
    /// </summary>
    public DateTime DateAssociation { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Obtient ou définit le score de confiance pour les associations automatiques (0.0 à 1.0).
    /// </summary>
    public decimal? ConfianceScore { get; set; }

    /// <summary>
    /// Obtient ou définit si cette association a été validée par un expert.
    /// </summary>
    public bool EstValidee { get; set; } = false;

    /// <summary>
    /// Obtient ou définit des commentaires sur l'association.
    /// </summary>
    [StringLength(500)]
    public string? Commentaires { get; set; }
}

/// <summary>
/// Énumération des types d'association possibles entre un terme et une métadonnée.
/// </summary>
public enum TypeAssociationTermeMetadonnee
{
    /// <summary>
    /// Association créée manuellement par un utilisateur.
    /// </summary>
    Manuel,

    /// <summary>
    /// Association suggérée automatiquement par le système.
    /// </summary>
    Automatique,

    /// <summary>
    /// Association suggérée par un algorithme d'apprentissage automatique.
    /// </summary>
    Suggere,

    /// <summary>
    /// Association héritée d'un actif parent.
    /// </summary>
    Herite,

    /// <summary>
    /// Association propagée via le lineage.
    /// </summary>
    Propage
}
