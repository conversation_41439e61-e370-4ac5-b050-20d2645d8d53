-- Script para agregar definiciones de metadatos de tipo "Lista" para pruebas
-- Este script agrega definiciones de tipo Lista a los esquemas existentes

USE [Gouvernance];
GO

-- Obtener el ID del esquema de tabla existente
DECLARE @SchemaTableId UNIQUEIDENTIFIER;
SELECT @SchemaTableId = Id 
FROM [Metadonnees].[SchemasMetadonnees] 
WHERE [TypeActif] = 'Table' AND [EstActif] = 1;

-- Verificar que el esquema existe
IF @SchemaTableId IS NULL
BEGIN
    PRINT 'Error: No se encontró un esquema activo para el tipo Table';
    RETURN;
END

PRINT 'Agregando definiciones de tipo Lista al esquema Table: ' + CAST(@SchemaTableId AS VARCHAR(36));

-- Agregar definición de tipo Lista para "Nivel de criticidad"
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id],
    [SchemaMetadonneesId],
    [Nom],
    [Description],
    [TypeDonnee],
    [EstObligatoire],
    [EstCalcule],
    [Ordre],
    [ValeurParDefaut],
    [ReglesValidation],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES (
    NEWID(),
    @SchemaTableId,
    'Niveau de criticité',
    'Niveau de criticité de la table pour l''organisation',
    'Liste',
    1, -- Obligatoire
    0, -- Pas calculé
    6, -- Ordre
    'Moyen',
    '{"options": ["Très faible", "Faible", "Moyen", "Élevé", "Critique"]}',
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

-- Agregar définition de tipo Lista para "Statut de qualité"
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id],
    [SchemaMetadonneesId],
    [Nom],
    [Description],
    [TypeDonnee],
    [EstObligatoire],
    [EstCalcule],
    [Ordre],
    [ValeurParDefaut],
    [ReglesValidation],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES (
    NEWID(),
    @SchemaTableId,
    'Statut de qualité',
    'Statut de la qualité des données dans la table',
    'Liste',
    0, -- Pas obligatoire
    0, -- Pas calculé
    7, -- Ordre
    'Non évalué',
    '{"options": ["Non évalué", "Excellent", "Bon", "Acceptable", "Médiocre", "Mauvais"]}',
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

-- Agregar définition de tipo Lista para "Environnement"
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id],
    [SchemaMetadonneesId],
    [Nom],
    [Description],
    [TypeDonnee],
    [EstObligatoire],
    [EstCalcule],
    [Ordre],
    [ValeurParDefaut],
    [ReglesValidation],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES (
    NEWID(),
    @SchemaTableId,
    'Environnement',
    'Environnement où se trouve la table',
    'Liste',
    1, -- Obligatoire
    0, -- Pas calculé
    8, -- Ordre
    'Production',
    '{"options": ["Développement", "Test", "Pré-production", "Production"]}',
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

-- Obtener el ID del esquema de columna existente
DECLARE @SchemaColumnId UNIQUEIDENTIFIER;
SELECT @SchemaColumnId = Id 
FROM [Metadonnees].[SchemasMetadonnees] 
WHERE [TypeActif] = 'Column' AND [EstActif] = 1;

-- Verificar que el esquema existe
IF @SchemaColumnId IS NULL
BEGIN
    PRINT 'Error: No se encontró un esquema activo para el tipo Column';
    RETURN;
END

PRINT 'Agregando definiciones de tipo Lista al esquema Column: ' + CAST(@SchemaColumnId AS VARCHAR(36));

-- Agregar définition de tipo Lista para "Clasificación de datos"
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id],
    [SchemaMetadonneesId],
    [Nom],
    [Description],
    [TypeDonnee],
    [EstObligatoire],
    [EstCalcule],
    [Ordre],
    [ValeurParDefaut],
    [ReglesValidation],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES (
    NEWID(),
    @SchemaColumnId,
    'Classification de données',
    'Classification de sensibilité des données de la colonne',
    'Liste',
    1, -- Obligatoire
    0, -- Pas calculé
    5, -- Ordre
    'Public',
    '{"options": ["Public", "Interne", "Confidentiel", "Restreint", "Secret"]}',
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

PRINT 'Definiciones de tipo Lista agregadas exitosamente!';
PRINT 'Total de definiciones agregadas: 4';
