using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Domain.Interfaces.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers.V1;

/// <summary>
/// Contrôleur pour la gestion des associations entre termes du glossaire et politiques.
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
public class AssociationsTermePolitiqueController : ControllerBase
{
    private readonly IDepotTermeGlossairePolitique _depotAssociation;
    private readonly IDepotTermeGlossaire _depotTermeGlossaire;
    private readonly IDepotPolitique _depotPolitique;
    private readonly ILogger<AssociationsTermePolitiqueController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance du contrôleur des associations terme-politique.
    /// </summary>
    public AssociationsTermePolitiqueController(
        IDepotTermeGlossairePolitique depotAssociation,
        IDepotTermeGlossaire depotTermeGlossaire,
        IDepotPolitique depotPolitique,
        ILogger<AssociationsTermePolitiqueController> logger)
    {
        _depotAssociation = depotAssociation;
        _depotTermeGlossaire = depotTermeGlossaire;
        _depotPolitique = depotPolitique;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les associations avec leurs entités liées.
    /// </summary>
    /// <returns>Liste des associations.</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirToutesLesAssociations()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirTousAvecEntitesLieesAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations terme-politique");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient une association par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association trouvée.</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TermeGlossairePolitique), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TermeGlossairePolitique>> ObtenirAssociationParId(Guid id)
    {
        try
        {
            var association = await _depotAssociation.ObtenirParIdAvecEntitesLieesAsync(id);
            if (association == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            return Ok(association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Liste des associations pour le terme.</returns>
    [HttpGet("terme/{termeGlossaireId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsParTerme(Guid termeGlossaireId)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParTermeGlossaireAsync(termeGlossaireId);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations pour le terme {TermeId}", termeGlossaireId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations pour une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Liste des associations pour la politique.</returns>
    [HttpGet("politique/{politiqueId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsParPolitique(Guid politiqueId)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParPolitiqueAsync(politiqueId);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations pour la politique {PolitiqueId}", politiqueId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par type d'application.
    /// </summary>
    /// <param name="typeApplication">Type d'application.</param>
    /// <returns>Liste des associations du type spécifié.</returns>
    [HttpGet("type-application/{typeApplication}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsParTypeApplication(string typeApplication)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParTypeApplicationAsync(typeApplication);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par type d'application {Type}", typeApplication);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par niveau d'impact.
    /// </summary>
    /// <param name="niveauImpact">Niveau d'impact.</param>
    /// <returns>Liste des associations du niveau spécifié.</returns>
    [HttpGet("niveau-impact/{niveauImpact}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsParNiveauImpact(string niveauImpact)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParNiveauImpactAsync(niveauImpact);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par niveau d'impact {Niveau}", niveauImpact);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations actives.
    /// </summary>
    /// <returns>Liste des associations actives.</returns>
    [HttpGet("actives")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsActives()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirAssociationsActivesAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations actives");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations obligatoires.
    /// </summary>
    /// <returns>Liste des associations obligatoires.</returns>
    [HttpGet("obligatoires")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsObligatoires()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirAssociationsObligatoiresAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations obligatoires");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par statut de conformité.
    /// </summary>
    /// <param name="statutConformite">Statut de conformité.</param>
    /// <returns>Liste des associations avec le statut spécifié.</returns>
    [HttpGet("statut-conformite/{statutConformite}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsParStatutConformite(string statutConformite)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParStatutConformiteAsync(statutConformite);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par statut de conformité {Statut}", statutConformite);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations en vigueur.
    /// </summary>
    /// <param name="date">Date de référence (optionnelle).</param>
    /// <returns>Liste des associations en vigueur.</returns>
    [HttpGet("en-vigueur")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsEnVigueur([FromQuery] DateTime? date = null)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirEnVigueurAsync(date);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations en vigueur");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par niveau de priorité.
    /// </summary>
    /// <param name="priorite">Niveau de priorité (1=Haute, 2=Moyenne, 3=Basse).</param>
    /// <returns>Liste des associations de la priorité spécifiée.</returns>
    [HttpGet("priorite/{priorite:int}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossairePolitique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossairePolitique>>> ObtenirAssociationsParPriorite(int priorite)
    {
        try
        {
            if (priorite < 1 || priorite > 3)
            {
                return BadRequest("La priorité doit être entre 1 (Haute) et 3 (Basse)");
            }

            var associations = await _depotAssociation.ObtenirParPrioriteAsync(priorite);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par priorité {Priorite}", priorite);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les statistiques des associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    [HttpGet("statistiques")]
    [ProducesResponseType(typeof(Dictionary<string, int>), StatusCodes.Status200OK)]
    public async Task<ActionResult<Dictionary<string, int>>> ObtenirStatistiquesAssociations()
    {
        try
        {
            var statistiques = await _depotAssociation.ObtenirStatistiquesAsync();
            return Ok(statistiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques des associations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée une nouvelle association entre un terme et une politique.
    /// </summary>
    /// <param name="association">Association à créer.</param>
    /// <returns>Association créée.</returns>
    [HttpPost]
    [ProducesResponseType(typeof(TermeGlossairePolitique), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TermeGlossairePolitique>> CreerAssociation([FromBody] TermeGlossairePolitique association)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Vérifier si l'association existe déjà
            if (await _depotAssociation.AssociationExisteAsync(association.TermeGlossaireId, association.PolitiqueId))
            {
                return BadRequest("Une association entre ce terme et cette politique existe déjà");
            }

            // Vérifier que le terme et la politique existent
            var terme = await _depotTermeGlossaire.ObtenirParIdAsync(association.TermeGlossaireId);
            if (terme == null)
            {
                return BadRequest($"Terme du glossaire avec l'ID {association.TermeGlossaireId} non trouvé");
            }

            var politique = await _depotPolitique.ObtenirParIdAsync(association.PolitiqueId);
            if (politique == null)
            {
                return BadRequest($"Politique avec l'ID {association.PolitiqueId} non trouvée");
            }

            association.Id = Guid.NewGuid();
            association.DateAssociation = DateTime.UtcNow;
            association.DateCreation = DateTime.UtcNow;
            association.DateModification = DateTime.UtcNow;

            await _depotAssociation.AjouterAsync(association);
            
            return CreatedAtAction(nameof(ObtenirAssociationParId), new { id = association.Id }, association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'association");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour le statut de conformité d'une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="statutConformite">Nouveau statut de conformité.</param>
    /// <param name="scoreConformite">Score de conformité (optionnel).</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpPatch("{id:guid}/conformite")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> MettreAJourConformite(Guid id, [FromBody] string statutConformite, [FromQuery] decimal? scoreConformite = null)
    {
        try
        {
            var resultat = await _depotAssociation.MettreAJourConformiteAsync(id, statutConformite, scoreConformite);
            if (!resultat)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            return Ok(new { message = "Statut de conformité mis à jour avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la conformité de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprime une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> SupprimerAssociation(Guid id)
    {
        try
        {
            var association = await _depotAssociation.ObtenirParIdAsync(id);
            if (association == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            await _depotAssociation.SupprimerAsync(association.Id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
