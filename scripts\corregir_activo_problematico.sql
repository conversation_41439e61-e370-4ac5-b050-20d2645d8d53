-- <PERSON><PERSON><PERSON> para corregir metadatos del activo problemático
-- Fecha: $(date)
-- Descripción: Corrige TypeId NULL en metadatos del activo 756ecb9e-3e1e-4826-b35c-898d629832ff

USE [Gouvernance];
GO

PRINT '🔧 CORRECCIÓN: Activo problemático 756ecb9e-3e1e-4826-b35c-898d629832ff';
PRINT '====================================================================';

-- 1. Verificar el estado actual
PRINT '';
PRINT '📊 ESTADO ANTES DE LA CORRECCIÓN:';
PRINT '----------------------------------';

SELECT 
    m.Id as MetadonneeId,
    m.Nom as MetadonneeN<PERSON>,
    m.<PERSON>,
    m.TypeId,
    m.Type as TypeLegacy,
    CASE 
        WHEN m.TypeId IS NULL THEN '❌ PROBLEMÁTICO'
        ELSE '✅ OK'
    END as Estado
FROM [Metadonnees].[Metadonnees] m
WHERE m.ActifDonneesId = '756ecb9e-3e1e-4826-b35c-898d629832ff'
ORDER BY m.Nom;

-- 2. Obtener el ID del tipo "Technique" por defecto
DECLARE @TechniqueTypeId UNIQUEIDENTIFIER;
SELECT @TechniqueTypeId = Id 
FROM [Metadonnees].[TypesMetadonnees] 
WHERE Nom = 'Technique';

IF @TechniqueTypeId IS NULL
BEGIN
    PRINT '❌ ERROR: No se encontró el tipo "Technique"';
    PRINT 'Tipos disponibles:';
    SELECT Id, Nom FROM [Metadonnees].[TypesMetadonnees] ORDER BY Nom;
    RETURN;
END

PRINT '';
PRINT '🎯 Tipo "Technique" encontrado: ' + CAST(@TechniqueTypeId AS VARCHAR(50));

-- 3. Actualizar metadatos con TypeId NULL
PRINT '';
PRINT '🔧 ACTUALIZANDO METADATOS CON TypeId NULL...';

UPDATE [Metadonnees].[Metadonnees] 
SET TypeId = @TechniqueTypeId
WHERE ActifDonneesId = '756ecb9e-3e1e-4826-b35c-898d629832ff'
  AND TypeId IS NULL;

DECLARE @RowsUpdated INT = @@ROWCOUNT;
PRINT '✅ Registros actualizados: ' + CAST(@RowsUpdated AS VARCHAR(10));

-- 4. Verificar el estado después de la corrección
PRINT '';
PRINT '📊 ESTADO DESPUÉS DE LA CORRECCIÓN:';
PRINT '------------------------------------';

SELECT 
    m.Id as MetadonneeId,
    m.Nom as MetadonneeNom,
    m.Valeur,
    m.TypeId,
    t.Nom as TipoNombre,
    m.Type as TypeLegacy,
    CASE 
        WHEN m.TypeId IS NULL THEN '❌ PROBLEMÁTICO'
        ELSE '✅ OK'
    END as Estado
FROM [Metadonnees].[Metadonnees] m
LEFT JOIN [Metadonnees].[TypesMetadonnees] t ON m.TypeId = t.Id
WHERE m.ActifDonneesId = '756ecb9e-3e1e-4826-b35c-898d629832ff'
ORDER BY m.Nom;

-- 5. Verificar que no queden metadatos con TypeId NULL en todo el sistema
PRINT '';
PRINT '🔍 VERIFICACIÓN GLOBAL:';
PRINT '-----------------------';

DECLARE @RemainingNulls INT;
SELECT @RemainingNulls = COUNT(*) 
FROM [Metadonnees].[Metadonnees] 
WHERE TypeId IS NULL;

IF @RemainingNulls = 0
BEGIN
    PRINT '✅ ¡Perfecto! No quedan metadatos con TypeId NULL en el sistema';
END
ELSE
BEGIN
    PRINT '⚠️ Advertencia: Aún quedan ' + CAST(@RemainingNulls AS VARCHAR(10)) + ' metadatos con TypeId NULL en otros activos';
    
    SELECT 
        a.Id as ActifId,
        a.Nom as ActifNom,
        COUNT(m.Id) as MetadatosConTypeIdNull
    FROM [Metadonnees].[ActifsDonnees] a
    INNER JOIN [Metadonnees].[Metadonnees] m ON a.Id = m.ActifDonneesId
    WHERE m.TypeId IS NULL
    GROUP BY a.Id, a.Nom
    ORDER BY MetadatosConTypeIdNull DESC;
END

PRINT '';
PRINT '✅ Corrección completada para el activo 756ecb9e-3e1e-4826-b35c-898d629832ff!';
