using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    /// <inheritdoc />
    public partial class MigrarDomainesAffaires : Migration
    {
        /// <inheritdoc />
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // 1. Crear la tabla DomainesAffaires
            migrationBuilder.CreateTable(
                name: "DomainesAffaires",
                schema: "Gouvernance",
                columns: table => new
                {
                    Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    Nom = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: false),
                    Description = table.Column<string>(type: "nvarchar(500)", maxLength: 500, nullable: true),
                    Code = table.Column<string>(type: "nvarchar(10)", maxLength: 10, nullable: true),
                    EstActif = table.Column<bool>(type: "bit", nullable: false),
                    Ordre = table.Column<int>(type: "int", nullable: false),
                    Couleur = table.Column<string>(type: "nvarchar(7)", maxLength: 7, nullable: true),
                    DateCreation = table.Column<DateTime>(type: "datetime2", nullable: false),
                    DateModification = table.Column<DateTime>(type: "datetime2", nullable: true),
                    UtilisateurCreation = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true),
                    UtilisateurModification = table.Column<string>(type: "nvarchar(100)", maxLength: 100, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_DomainesAffaires", x => x.Id);
                });

            // 2. Insérer les domaines d'affaires existants basés sur les valeurs uniques de la colonne DomaineAffaires
            migrationBuilder.Sql(@"
                INSERT INTO [Gouvernance].[DomainesAffaires] (Id, Nom, Description, EstActif, Ordre, DateCreation)
                SELECT
                    NEWID() as Id,
                    DomaineAffaires as Nom,
                    'Domaine migré automatiquement' as Description,
                    1 as EstActif,
                    ROW_NUMBER() OVER (ORDER BY DomaineAffaires) as Ordre,
                    GETUTCDATE() as DateCreation
                FROM (
                    SELECT DISTINCT DomaineAffaires
                    FROM [Gouvernance].[TermesGlossaire]
                    WHERE DomaineAffaires IS NOT NULL AND DomaineAffaires != ''
                ) AS UniquesDomaines
            ");

            // 3. Ajouter la nouvelle colonne DomaineAffairesId
            migrationBuilder.AddColumn<Guid>(
                name: "DomaineAffairesId",
                schema: "Gouvernance",
                table: "TermesGlossaire",
                type: "uniqueidentifier",
                nullable: true);

            // 4. Mettre à jour les références
            migrationBuilder.Sql(@"
                UPDATE tg 
                SET DomaineAffairesId = da.Id
                FROM [Gouvernance].[TermesGlossaire] tg
                INNER JOIN [Gouvernance].[DomainesAffaires] da ON tg.DomaineAffaires = da.Nom
                WHERE tg.DomaineAffaires IS NOT NULL AND tg.DomaineAffaires != ''
            ");

            // 5. Créer la clé étrangère
            migrationBuilder.CreateIndex(
                name: "IX_TermesGlossaire_DomaineAffairesId",
                schema: "Gouvernance",
                table: "TermesGlossaire",
                column: "DomaineAffairesId");

            migrationBuilder.AddForeignKey(
                name: "FK_TermesGlossaire_DomainesAffaires_DomaineAffairesId",
                schema: "Gouvernance",
                table: "TermesGlossaire",
                column: "DomaineAffairesId",
                principalSchema: "Gouvernance",
                principalTable: "DomainesAffaires",
                principalColumn: "Id");

            // 6. Supprimer l'ancienne colonne DomaineAffaires
            migrationBuilder.DropColumn(
                name: "DomaineAffaires",
                schema: "Gouvernance",
                table: "TermesGlossaire");
        }

        /// <inheritdoc />
        protected override void Down(MigrationBuilder migrationBuilder)
        {
            // 1. Ajouter l'ancienne colonne DomaineAffaires
            migrationBuilder.AddColumn<string>(
                name: "DomaineAffaires",
                schema: "Gouvernance",
                table: "TermesGlossaire",
                type: "nvarchar(100)",
                maxLength: 100,
                nullable: false,
                defaultValue: "");

            // 2. Restaurer les valeurs
            migrationBuilder.Sql(@"
                UPDATE tg 
                SET DomaineAffaires = da.Nom
                FROM [Gouvernance].[TermesGlossaire] tg
                INNER JOIN [Gouvernance].[DomainesAffaires] da ON tg.DomaineAffairesId = da.Id
                WHERE tg.DomaineAffairesId IS NOT NULL
            ");

            // 3. Supprimer la clé étrangère et l'index
            migrationBuilder.DropForeignKey(
                name: "FK_TermesGlossaire_DomainesAffaires_DomaineAffairesId",
                schema: "Gouvernance",
                table: "TermesGlossaire");

            migrationBuilder.DropIndex(
                name: "IX_TermesGlossaire_DomaineAffairesId",
                schema: "Gouvernance",
                table: "TermesGlossaire");

            // 4. Supprimer la nouvelle colonne
            migrationBuilder.DropColumn(
                name: "DomaineAffairesId",
                schema: "Gouvernance",
                table: "TermesGlossaire");

            // 5. Supprimer la table DomainesAffaires
            migrationBuilder.DropTable(
                name: "DomainesAffaires",
                schema: "Gouvernance");
        }
    }
}
