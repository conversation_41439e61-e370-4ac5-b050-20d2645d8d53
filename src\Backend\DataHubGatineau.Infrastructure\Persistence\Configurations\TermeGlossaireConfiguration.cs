using DataHubGatineau.Domain.Entites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataHubGatineau.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration de l'entité TermeGlossaire pour Entity Framework Core.
/// </summary>
public class TermeGlossaireConfiguration : IEntityTypeConfiguration<TermeGlossaire>
{
    /// <summary>
    /// Configure l'entité TermeGlossaire.
    /// </summary>
    /// <param name="builder">Constructeur de type d'entité.</param>
    public void Configure(EntityTypeBuilder<TermeGlossaire> builder)
    {
        builder.HasKey(t => t.Id);

        builder.Property(t => t.Nom)
            .IsRequired()
            .HasMaxLength(100);

        builder.Property(t => t.Definition)
            .IsRequired()
            .HasMaxLength(1000);

        builder.Property(t => t.Exemples)
            .HasMaxLength(500);

        builder.Property(t => t.Synonymes)
            .HasMaxLength(500);

        builder.Property(t => t.Proprietaire)
            .HasMaxLength(100);

        // Configuration des relations
        builder.HasOne(t => t.DomaineAffaires)
            .WithMany(d => d.TermesGlossaire)
            .HasForeignKey(t => t.DomaineAffairesId)
            .IsRequired(false);

        builder.HasOne(t => t.TermeParent)
            .WithMany(t => t.TermesEnfants)
            .HasForeignKey(t => t.TermeParentId)
            .OnDelete(DeleteBehavior.Restrict)
            .IsRequired(false);

        builder.HasMany(t => t.ActifsDonnees)
            .WithMany(e => e.TermesGlossaire)
            .UsingEntity(j => j.ToTable("ActifsTermes", "Gouvernance"));
    }
}
