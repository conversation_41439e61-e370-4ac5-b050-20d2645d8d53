using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Domain.Entites;

/// <summary>
/// Représente l'association entre un terme du glossaire et une politique.
/// Cette entité permet d'appliquer des politiques basées sur la classification sémantique.
/// </summary>
public class TermeGlossairePolitique : EntiteBase
{
    /// <summary>
    /// Obtient ou définit l'identifiant du terme du glossaire.
    /// </summary>
    [Required]
    public Guid TermeGlossaireId { get; set; }

    /// <summary>
    /// Obtient ou définit le terme du glossaire associé.
    /// </summary>
    public virtual TermeGlossaire TermeGlossaire { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit l'identifiant de la politique.
    /// </summary>
    [Required]
    public Guid PolitiqueId { get; set; }

    /// <summary>
    /// Obtient ou définit la politique associée.
    /// </summary>
    public virtual Politique Politique { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit le type d'application de la politique pour ce terme.
    /// </summary>
    [Required]
    [StringLength(50)]
    public string TypeApplication { get; set; } = "Classification";

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé cette association.
    /// </summary>
    [StringLength(100)]
    public string? UtilisateurAssociation { get; set; }

    /// <summary>
    /// Obtient ou définit la date de création de l'association.
    /// </summary>
    public DateTime DateAssociation { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Obtient ou définit le niveau d'impact de cette politique sur le terme.
    /// </summary>
    [Required]
    [StringLength(20)]
    public string NiveauImpact { get; set; } = "Moyen";

    /// <summary>
    /// Obtient ou définit si cette politique est obligatoire pour le terme.
    /// </summary>
    public bool EstObligatoire { get; set; } = false;

    /// <summary>
    /// Obtient ou définit la priorité d'application de cette politique (1 = haute, 5 = basse).
    /// </summary>
    public int Priorite { get; set; } = 3;

    /// <summary>
    /// Obtient ou définit la date d'entrée en vigueur spécifique pour ce terme.
    /// </summary>
    public DateTime? DateEntreeVigueurSpecifique { get; set; }

    /// <summary>
    /// Obtient ou définit la date d'expiration spécifique pour ce terme.
    /// </summary>
    public DateTime? DateExpirationSpecifique { get; set; }

    /// <summary>
    /// Obtient ou définit des paramètres spécifiques pour l'application de cette politique.
    /// </summary>
    [StringLength(1000)]
    public string? ParametresSpecifiques { get; set; }

    /// <summary>
    /// Obtient ou définit des commentaires sur l'association.
    /// </summary>
    [StringLength(500)]
    public string? Commentaires { get; set; }

    /// <summary>
    /// Obtient ou définit si cette association est active.
    /// </summary>
    public bool EstActive { get; set; } = true;

    /// <summary>
    /// Obtient ou définit le statut de conformité actuel.
    /// </summary>
    [StringLength(50)]
    public string? StatutConformite { get; set; }

    /// <summary>
    /// Obtient ou définit la date de la dernière évaluation de conformité.
    /// </summary>
    public DateTime? DateDerniereEvaluation { get; set; }

    /// <summary>
    /// Obtient ou définit le score de conformité (0-100).
    /// </summary>
    public decimal? ScoreConformite { get; set; }
}

/// <summary>
/// Énumération des types d'application possibles entre un terme et une politique.
/// </summary>
public enum TypeApplicationTermePolitique
{
    /// <summary>
    /// Politique appliquée pour la classification des données.
    /// </summary>
    Classification,

    /// <summary>
    /// Politique appliquée pour le contrôle d'accès.
    /// </summary>
    Acces,

    /// <summary>
    /// Politique appliquée pour la rétention des données.
    /// </summary>
    Retention,

    /// <summary>
    /// Politique appliquée pour la confidentialité.
    /// </summary>
    Confidentialite,

    /// <summary>
    /// Politique appliquée pour la conformité réglementaire.
    /// </summary>
    Conformite,

    /// <summary>
    /// Politique appliquée pour la qualité des données.
    /// </summary>
    Qualite,

    /// <summary>
    /// Politique appliquée pour la sécurité des données.
    /// </summary>
    Securite,

    /// <summary>
    /// Politique appliquée pour l'archivage des données.
    /// </summary>
    Archivage,

    /// <summary>
    /// Politique personnalisée selon les besoins spécifiques.
    /// </summary>
    Personnalisee
}

/// <summary>
/// Énumération des niveaux d'impact possibles d'une politique sur un terme.
/// </summary>
public enum NiveauImpactTermePolitique
{
    /// <summary>
    /// Impact faible - politique informative.
    /// </summary>
    Faible,

    /// <summary>
    /// Impact moyen - politique recommandée.
    /// </summary>
    Moyen,

    /// <summary>
    /// Impact élevé - politique importante.
    /// </summary>
    Eleve,

    /// <summary>
    /// Impact critique - politique obligatoire.
    /// </summary>
    Critique
}
