using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Domain.Interfaces.Repositories;

/// <summary>
/// Interface du dépôt pour les associations entre termes du glossaire et métadonnées.
/// </summary>
public interface IDepotTermeGlossaireMetadonnee : IDepotBase<TermeGlossaireMetadonnee>
{
    /// <summary>
    /// Obtient toutes les associations avec les entités liées.
    /// </summary>
    /// <returns>Collection d'associations avec les entités liées.</returns>
    Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirTousAvecEntitesLieesAsync();

    /// <summary>
    /// Obtient une association par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association avec les entités liées si trouvée.</returns>
    Task<TermeGlossaireMetadonnee?> ObtenirParIdAvecEntitesLieesAsync(Guid id);

    /// <summary>
    /// Obtient toutes les associations pour un terme du glossaire spécifique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Collection d'associations pour le terme spécifié.</returns>
    Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirParTermeGlossaireAsync(Guid termeGlossaireId);

    /// <summary>
    /// Obtient toutes les associations pour une métadonnée spécifique.
    /// </summary>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>Collection d'associations pour la métadonnée spécifiée.</returns>
    Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirParMetadonneeAsync(Guid metadonneeId);

    /// <summary>
    /// Obtient les associations par type d'association.
    /// </summary>
    /// <param name="typeAssociation">Type d'association.</param>
    /// <returns>Collection d'associations du type spécifié.</returns>
    Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirParTypeAssociationAsync(string typeAssociation);

    /// <summary>
    /// Obtient les associations validées.
    /// </summary>
    /// <returns>Collection d'associations validées.</returns>
    Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirAssociationsValideesAsync();

    /// <summary>
    /// Obtient les associations suggérées avec un score de confiance minimum.
    /// </summary>
    /// <param name="scoreMinimum">Score de confiance minimum.</param>
    /// <returns>Collection d'associations suggérées.</returns>
    Task<IEnumerable<TermeGlossaireMetadonnee>> ObtenirSuggestionsAsync(decimal scoreMinimum = 0.7m);

    /// <summary>
    /// Vérifie si une association existe déjà entre un terme et une métadonnée.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>True si l'association existe, false sinon.</returns>
    Task<bool> AssociationExisteAsync(Guid termeGlossaireId, Guid metadonneeId);

    /// <summary>
    /// Obtient les statistiques d'associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    Task<Dictionary<string, int>> ObtenirStatistiquesAsync();

    /// <summary>
    /// Supprime toutes les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    Task<int> SupprimerParTermeGlossaireAsync(Guid termeGlossaireId);

    /// <summary>
    /// Supprime toutes les associations pour une métadonnée.
    /// </summary>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    Task<int> SupprimerParMetadonneeAsync(Guid metadonneeId);
}
