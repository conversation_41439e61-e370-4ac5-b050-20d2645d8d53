@page "/termes-glossaire/{Id:guid}"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@inject ITermeGlossaireService TermeGlossaireService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>@(_terme?.Nom ?? "Terme") - Glossaire - DataHub Gatineau</PageTitle>

@if (_loading)
{
    <div class="d-flex justify-content-center my-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Chargement...</span>
        </div>
    </div>
}
else if (_terme == null)
{
    <div class="alert alert-danger">
        <h4>Terme non trouvé</h4>
        <p>Le terme demandé n'existe pas ou a été supprimé.</p>
        <a href="/glossaire" class="btn btn-primary">Retour au glossaire</a>
    </div>
}
else
{
    <!-- Breadcrumb -->
    <nav aria-label="breadcrumb" class="mb-4">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="/">Accueil</a></li>
            <li class="breadcrumb-item"><a href="/glossaire">Glossaire</a></li>
            <li class="breadcrumb-item active" aria-current="page">@_terme.Nom</li>
        </ol>
    </nav>

    <!-- Header -->
    <div class="terme-header mb-4">
        <div class="d-flex justify-content-between align-items-start">
            <div>
                <h1 class="display-6 mb-2">@_terme.Nom</h1>
                <div class="terme-meta">
                    @if (!string.IsNullOrEmpty(_terme.DomaineAffaires))
                    {
                        <span class="badge bg-primary me-2">@_terme.DomaineAffaires</span>
                    }
                    <span class="text-muted">
                        <i class="bi bi-person me-1"></i>
                        Propriétaire: @_terme.Proprietaire
                    </span>
                </div>
            </div>
            <div class="terme-actions">
                <button class="btn btn-outline-primary me-2" @onclick="ModifierTerme">
                    <i class="bi bi-pencil me-1"></i>
                    Modifier
                </button>
                <button class="btn btn-outline-danger" @onclick="SupprimerTerme">
                    <i class="bi bi-trash me-1"></i>
                    Supprimer
                </button>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Contenu principal -->
        <div class="col-lg-8">
            <!-- Définition -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-book me-2"></i>
                        Définition
                    </h5>
                </div>
                <div class="card-body">
                    <p class="lead">@_terme.Definition</p>
                </div>
            </div>

            <!-- Exemples -->
            @if (!string.IsNullOrEmpty(_terme.Exemples))
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-lightbulb me-2"></i>
                            Exemples
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="exemples-content">
                            @((MarkupString)_terme.Exemples.Replace("\n", "<br>"))
                        </div>
                    </div>
                </div>
            }

            <!-- Synonymes -->
            @if (!string.IsNullOrEmpty(_terme.Synonymes))
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-arrow-repeat me-2"></i>
                            Synonymes
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="synonymes-tags">
                            @foreach (var synonyme in _terme.Synonymes.Split(',', StringSplitOptions.RemoveEmptyEntries))
                            {
                                <span class="badge bg-light text-dark me-2 mb-2">@synonyme.Trim()</span>
                            }
                        </div>
                    </div>
                </div>
            }

            <!-- Termes enfants -->
            @if (_termesEnfants.Any())
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-diagram-3 me-2"></i>
                            Termes associés
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            @foreach (var enfant in _termesEnfants)
                            {
                                <div class="col-md-6 mb-3">
                                    <div class="terme-enfant-card">
                                        <h6><a href="/termes-glossaire/@enfant.Id">@enfant.Nom</a></h6>
                                        <p class="text-muted small">@(enfant.Definition.Length > 100 ? enfant.Definition.Substring(0, 97) + "..." : enfant.Definition)</p>
                                    </div>
                                </div>
                            }
                        </div>
                    </div>
                </div>
            }
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Informations -->
            <div class="card mb-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-info-circle me-2"></i>
                        Informations
                    </h5>
                </div>
                <div class="card-body">
                    <div class="info-item mb-3">
                        <strong>Propriétaire:</strong>
                        <div>@_terme.Proprietaire</div>
                    </div>
                    @if (_domaine != null)
                    {
                        <div class="info-item mb-3">
                            <strong>Domaine de gouvernance:</strong>
                            <div>
                                <a href="/domaines-gouvernance/@_domaine.Id">@_domaine.Nom</a>
                            </div>
                        </div>
                    }
                    @if (!string.IsNullOrEmpty(_terme.DomaineAffaires))
                    {
                        <div class="info-item mb-3">
                            <strong>Domaine d'affaires:</strong>
                            <div>@_terme.DomaineAffaires</div>
                        </div>
                    }
                    <div class="info-item mb-3">
                        <strong>Créé le:</strong>
                        <div>@_terme.DateCreation.ToString("dd/MM/yyyy")</div>
                    </div>
                    <div class="info-item">
                        <strong>Modifié le:</strong>
                        <div>@_terme.DateModification.ToString("dd/MM/yyyy")</div>
                    </div>
                </div>
            </div>

            <!-- Terme parent -->
            @if (_termeParent != null)
            {
                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-arrow-up me-2"></i>
                            Terme parent
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="terme-parent-card">
                            <h6><a href="/termes-glossaire/@_termeParent.Id">@_termeParent.Nom</a></h6>
                            <p class="text-muted small">@(_termeParent.Definition.Length > 80 ? _termeParent.Definition.Substring(0, 77) + "..." : _termeParent.Definition)</p>
                        </div>
                    </div>
                </div>
            }

            <!-- Actions rapides -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="bi bi-lightning me-2"></i>
                        Actions rapides
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <button class="btn btn-outline-primary" @onclick="ModifierTerme">
                            <i class="bi bi-pencil me-1"></i>
                            Modifier ce terme
                        </button>
                        <button class="btn btn-outline-success" @onclick="AjouterTermeEnfant">
                            <i class="bi bi-plus-circle me-1"></i>
                            Ajouter un sous-terme
                        </button>
                        <a href="/glossaire" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-1"></i>
                            Retour au glossaire
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
}

<style>
    .terme-header {
        background: linear-gradient(135deg, #f8f9fa, #e9ecef);
        padding: 2rem;
        border-radius: 12px;
        border: 1px solid #dee2e6;
    }

    .terme-meta .badge {
        font-size: 0.8rem;
    }

    .terme-actions .btn {
        min-width: 120px;
    }

    .exemples-content {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border-left: 4px solid #007bff;
    }

    .synonymes-tags .badge {
        font-size: 0.9rem;
        padding: 0.5rem 0.75rem;
    }

    .terme-enfant-card, .terme-parent-card {
        background: #f8f9fa;
        padding: 1rem;
        border-radius: 8px;
        border: 1px solid #e9ecef;
    }

    .terme-enfant-card h6 a, .terme-parent-card h6 a {
        color: #007bff;
        text-decoration: none;
    }

    .terme-enfant-card h6 a:hover, .terme-parent-card h6 a:hover {
        text-decoration: underline;
    }

    .info-item strong {
        color: #495057;
        font-size: 0.9rem;
    }

    .info-item div {
        margin-top: 0.25rem;
    }
</style>

@code {
    [Parameter] public Guid Id { get; set; }

    private TermeGlossaire? _terme;
    private TermeGlossaire? _termeParent;
    private DomaineGouvernance? _domaine;
    private List<TermeGlossaire> _termesEnfants = new();
    private bool _loading = true;

    protected override async Task OnInitializedAsync()
    {
        await ChargerTerme();
    }

    protected override async Task OnParametersSetAsync()
    {
        if (_terme?.Id != Id)
        {
            await ChargerTerme();
        }
    }

    private async Task ChargerTerme()
    {
        _loading = true;
        try
        {
            // Charger le terme principal
            _terme = await TermeGlossaireService.ObtenirParIdAsync(Id);

            if (_terme != null)
            {
                // Charger le terme parent si existe
                if (_terme.TermeParentId.HasValue)
                {
                    try
                    {
                        // Note: Adapter selon l'interface disponible
                        var termesParents = await TermeGlossaireService.ObtenirTousAsync();
                        _termeParent = termesParents.FirstOrDefault(t => t.Id.ToString() == _terme.TermeParentId.ToString());
                    }
                    catch (Exception ex)
                    {
                        Console.Error.WriteLine($"Erreur lors du chargement du terme parent: {ex.Message}");
                    }
                }

                // Charger les termes enfants
                try
                {
                    var termesEnfants = await TermeGlossaireService.ObtenirSousTermesAsync(Id);
                    _termesEnfants = termesEnfants.ToList();
                }
                catch (Exception ex)
                {
                    Console.Error.WriteLine($"Erreur lors du chargement des termes enfants: {ex.Message}");
                }

                // Charger le domaine de gouvernance
                if (_terme.DomaineGouvernanceId.HasValue)
                {
                    try
                    {
                        _domaine = await DomaineGouvernanceService.ObtenirParIdAsync(_terme.DomaineGouvernanceId.Value);
                    }
                    catch (Exception ex)
                    {
                        Console.Error.WriteLine($"Erreur lors du chargement du domaine: {ex.Message}");
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement du terme: {ex.Message}");
            _terme = null;
        }
        finally
        {
            _loading = false;
        }
    }

    private void ModifierTerme()
    {
        NavigationManager.NavigateTo($"/termes-glossaire/modifier/{Id}");
    }

    private void AjouterTermeEnfant()
    {
        NavigationManager.NavigateTo($"/termes-glossaire/ajouter?parent={Id}");
    }

    private async Task SupprimerTerme()
    {
        if (_terme == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Êtes-vous sûr de vouloir supprimer le terme '{_terme.Nom}' ?\n\nCette action est irréversible.");

        if (confirmed)
        {
            try
            {
                await TermeGlossaireService.SupprimerAsync(Id);
                NavigationManager.NavigateTo("/glossaire");
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Erreur lors de la suppression: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression du terme.");
            }
        }
    }
}
