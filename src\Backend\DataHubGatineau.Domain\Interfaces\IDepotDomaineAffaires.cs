using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Domain.Interfaces;

/// <summary>
/// Interface pour le dépôt des domaines d'affaires.
/// </summary>
public interface IDepotDomaineAffaires : IDepotBase<DomaineAffaires>
{
    /// <summary>
    /// Obtient tous les domaines d'affaires actifs, triés par ordre d'affichage.
    /// </summary>
    /// <returns>Une collection de domaines d'affaires actifs.</returns>
    Task<IEnumerable<DomaineAffaires>> ObtenirActifsAsync();

    /// <summary>
    /// Obtient un domaine d'affaires par son code.
    /// </summary>
    /// <param name="code">Code du domaine d'affaires.</param>
    /// <returns>Le domaine d'affaires correspondant au code, ou null si non trouvé.</returns>
    Task<DomaineAffaires?> ObtenirParCodeAsync(string code);

    /// <summary>
    /// Vérifie si un nom de domaine d'affaires existe déjà.
    /// </summary>
    /// <param name="nom">Nom à vérifier.</param>
    /// <param name="idExclure">ID à exclure de la vérification (pour les modifications).</param>
    /// <returns>True si le nom existe déjà, false sinon.</returns>
    Task<bool> NomExisteAsync(string nom, Guid? idExclure = null);

    /// <summary>
    /// Vérifie si un code de domaine d'affaires existe déjà.
    /// </summary>
    /// <param name="code">Code à vérifier.</param>
    /// <param name="idExclure">ID à exclure de la vérification (pour les modifications).</param>
    /// <returns>True si le code existe déjà, false sinon.</returns>
    Task<bool> CodeExisteAsync(string code, Guid? idExclure = null);

    /// <summary>
    /// Active ou désactive un domaine d'affaires.
    /// </summary>
    /// <param name="id">ID du domaine d'affaires.</param>
    /// <param name="estActif">Nouvel état d'activation.</param>
    /// <returns>True si l'opération a réussi, false sinon.</returns>
    Task<bool> ChangerEtatActivationAsync(Guid id, bool estActif);

    /// <summary>
    /// Obtient les statistiques d'utilisation d'un domaine d'affaires.
    /// </summary>
    /// <param name="id">ID du domaine d'affaires.</param>
    /// <returns>Tuple contenant le nombre de termes, date du premier et dernier terme.</returns>
    Task<(int NombreTermes, DateTime? DatePremier, DateTime? DateDernier)> ObtenirStatistiquesUtilisationAsync(Guid id);
}
