using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Enums;
using DataHubGatineau.Domain.Interfaces.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers.V1;

/// <summary>
/// Contrôleur pour la gestion des politiques de gouvernance des données.
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
public class PolitiquesController : ControllerBase
{
    private readonly IDepotPolitique _depotPolitique;
    private readonly ILogger<PolitiquesController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance du contrôleur des politiques.
    /// </summary>
    /// <param name="depotPolitique">Dépôt des politiques.</param>
    /// <param name="logger">Logger pour les traces.</param>
    public PolitiquesController(
        IDepotPolitique depotPolitique,
        ILogger<PolitiquesController> logger)
    {
        _depotPolitique = depotPolitique;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les politiques avec leurs entités liées.
    /// </summary>
    /// <returns>Liste des politiques.</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirToutesLesPolitiques()
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirTousAvecEntitesLieesAsync();
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient une politique par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <returns>Politique trouvée.</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(Politique), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Politique>> ObtenirPolitiqueParId(Guid id)
    {
        try
        {
            var politique = await _depotPolitique.ObtenirParIdAvecEntitesLieesAsync(id);
            if (politique == null)
            {
                return NotFound($"Politique avec l'ID {id} non trouvée");
            }

            return Ok(politique);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la politique {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient une politique par son code.
    /// </summary>
    /// <param name="code">Code de la politique.</param>
    /// <returns>Politique trouvée.</returns>
    [HttpGet("code/{code}")]
    [ProducesResponseType(typeof(Politique), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Politique>> ObtenirPolitiqueParCode(string code)
    {
        try
        {
            var politique = await _depotPolitique.ObtenirParCodeAsync(code);
            if (politique == null)
            {
                return NotFound($"Politique avec le code {code} non trouvée");
            }

            return Ok(politique);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de la politique avec le code {Code}", code);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les politiques par catégorie.
    /// </summary>
    /// <param name="categorie">Catégorie des politiques.</param>
    /// <returns>Liste des politiques de la catégorie.</returns>
    [HttpGet("categorie/{categorie}")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirPolitiquesParCategorie(string categorie)
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirParCategorieAsync(categorie);
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques par catégorie {Categorie}", categorie);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les politiques par statut.
    /// </summary>
    /// <param name="statut">Statut des politiques.</param>
    /// <returns>Liste des politiques du statut.</returns>
    [HttpGet("statut/{statut}")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirPolitiquesParStatut(StatutPolitique statut)
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirParStatutAsync(statut);
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques par statut {Statut}", statut);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les politiques actives.
    /// </summary>
    /// <returns>Liste des politiques actives.</returns>
    [HttpGet("actives")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirPolitiquesActives()
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirPolitiquesActivesAsync();
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques actives");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les politiques en vigueur.
    /// </summary>
    /// <param name="date">Date de référence (optionnelle).</param>
    /// <returns>Liste des politiques en vigueur.</returns>
    [HttpGet("en-vigueur")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirPolitiquesEnVigueur([FromQuery] DateTime? date = null)
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirEnVigueurAsync(date);
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques en vigueur");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les politiques qui expirent bientôt.
    /// </summary>
    /// <param name="joursAvantExpiration">Nombre de jours avant expiration.</param>
    /// <returns>Liste des politiques qui expirent bientôt.</returns>
    [HttpGet("expirant-bientot")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirPolitiquesExpirantBientot([FromQuery] int joursAvantExpiration = 30)
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirExpirantBientotAsync(joursAvantExpiration);
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques expirant bientôt");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les politiques nécessitant une révision.
    /// </summary>
    /// <returns>Liste des politiques à réviser.</returns>
    [HttpGet("a-reviser")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> ObtenirPolitiquesAReviser()
    {
        try
        {
            var politiques = await _depotPolitique.ObtenirAReviserAsync();
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des politiques à réviser");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Recherche des politiques par mots-clés.
    /// </summary>
    /// <param name="motsCles">Mots-clés de recherche.</param>
    /// <returns>Liste des politiques correspondantes.</returns>
    [HttpGet("rechercher")]
    [ProducesResponseType(typeof(IEnumerable<Politique>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<Politique>>> RechercherPolitiques([FromQuery] string motsCles)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(motsCles))
            {
                return BadRequest("Les mots-clés de recherche sont requis");
            }

            var politiques = await _depotPolitique.RechercherParMotsClesAsync(motsCles);
            return Ok(politiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la recherche de politiques avec les mots-clés {MotsCles}", motsCles);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les statistiques des politiques.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    [HttpGet("statistiques")]
    [ProducesResponseType(typeof(Dictionary<string, int>), StatusCodes.Status200OK)]
    public async Task<ActionResult<Dictionary<string, int>>> ObtenirStatistiquesPolitiques()
    {
        try
        {
            var statistiques = await _depotPolitique.ObtenirStatistiquesAsync();
            return Ok(statistiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques des politiques");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée une nouvelle politique.
    /// </summary>
    /// <param name="politique">Politique à créer.</param>
    /// <returns>Politique créée.</returns>
    [HttpPost]
    [ProducesResponseType(typeof(Politique), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<Politique>> CreerPolitique([FromBody] Politique politique)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Vérifier si le code existe déjà
            if (await _depotPolitique.CodeExisteAsync(politique.Code))
            {
                return BadRequest($"Une politique avec le code {politique.Code} existe déjà");
            }

            politique.Id = Guid.NewGuid();
            politique.DateCreation = DateTime.UtcNow;
            politique.DateModification = DateTime.UtcNow;

            await _depotPolitique.AjouterAsync(politique);
            
            return CreatedAtAction(nameof(ObtenirPolitiqueParId), new { id = politique.Id }, politique);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de la politique");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour une politique existante.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="politique">Données de la politique à mettre à jour.</param>
    /// <returns>Politique mise à jour.</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(Politique), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<Politique>> MettreAJourPolitique(Guid id, [FromBody] Politique politique)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (id != politique.Id)
            {
                return BadRequest("L'ID dans l'URL ne correspond pas à l'ID de la politique");
            }

            var politiqueExistante = await _depotPolitique.ObtenirParIdAsync(id);
            if (politiqueExistante == null)
            {
                return NotFound($"Politique avec l'ID {id} non trouvée");
            }

            // Vérifier si le code existe déjà pour une autre politique
            if (await _depotPolitique.CodeExisteAsync(politique.Code, id))
            {
                return BadRequest($"Une autre politique avec le code {politique.Code} existe déjà");
            }

            politique.DateModification = DateTime.UtcNow;
            await _depotPolitique.MettreAJourAsync(politique);

            return Ok(politique);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de la politique {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour le statut d'une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="nouveauStatut">Nouveau statut.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpPatch("{id:guid}/statut")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> MettreAJourStatutPolitique(Guid id, [FromBody] StatutPolitique nouveauStatut)
    {
        try
        {
            var resultat = await _depotPolitique.MettreAJourStatutAsync(id, nouveauStatut);
            if (!resultat)
            {
                return NotFound($"Politique avec l'ID {id} non trouvée");
            }

            return Ok(new { message = "Statut mis à jour avec succès" });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour du statut de la politique {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Active ou désactive une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <param name="estActive">État d'activation.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpPatch("{id:guid}/activation")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> ChangerEtatActivationPolitique(Guid id, [FromBody] bool estActive)
    {
        try
        {
            var resultat = await _depotPolitique.ChangerEtatActivationAsync(id, estActive);
            if (!resultat)
            {
                return NotFound($"Politique avec l'ID {id} non trouvée");
            }

            var message = estActive ? "Politique activée avec succès" : "Politique désactivée avec succès";
            return Ok(new { message });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors du changement d'état d'activation de la politique {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprime une politique.
    /// </summary>
    /// <param name="id">Identifiant de la politique.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> SupprimerPolitique(Guid id)
    {
        try
        {
            var politique = await _depotPolitique.ObtenirParIdAsync(id);
            if (politique == null)
            {
                return NotFound($"Politique avec l'ID {id} non trouvée");
            }

            await _depotPolitique.SupprimerAsync(politique.Id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de la politique {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
