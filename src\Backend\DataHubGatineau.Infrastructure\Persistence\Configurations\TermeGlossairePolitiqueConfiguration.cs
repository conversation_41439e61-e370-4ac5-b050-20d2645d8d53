using DataHubGatineau.Domain.Entites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataHubGatineau.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration de l'entité TermeGlossairePolitique pour Entity Framework Core.
/// </summary>
public class TermeGlossairePolitiqueConfiguration : IEntityTypeConfiguration<TermeGlossairePolitique>
{
    /// <summary>
    /// Configure l'entité TermeGlossairePolitique.
    /// </summary>
    /// <param name="builder">Constructeur de type d'entité.</param>
    public void Configure(EntityTypeBuilder<TermeGlossairePolitique> builder)
    {
        // Configuration de la table
        builder.ToTable("TermesGlossairePolitiques", "Gouvernance");

        // Configuration de la clé primaire
        builder.HasKey(tgp => tgp.Id);

        // Configuration des propriétés
        builder.Property(tgp => tgp.TermeGlossaireId)
            .IsRequired();

        builder.Property(tgp => tgp.PolitiqueId)
            .IsRequired();

        builder.Property(tgp => tgp.TypeApplication)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("Classification");

        builder.Property(tgp => tgp.UtilisateurAssociation)
            .HasMaxLength(100);

        builder.Property(tgp => tgp.DateAssociation)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(tgp => tgp.NiveauImpact)
            .IsRequired()
            .HasMaxLength(20)
            .HasDefaultValue("Moyen");

        builder.Property(tgp => tgp.EstObligatoire)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(tgp => tgp.Priorite)
            .IsRequired()
            .HasDefaultValue(3);

        builder.Property(tgp => tgp.ParametresSpecifiques)
            .HasMaxLength(1000);

        builder.Property(tgp => tgp.Commentaires)
            .HasMaxLength(500);

        builder.Property(tgp => tgp.EstActive)
            .IsRequired()
            .HasDefaultValue(true);

        builder.Property(tgp => tgp.StatutConformite)
            .HasMaxLength(50);

        builder.Property(tgp => tgp.ScoreConformite)
            .HasPrecision(5, 2);

        // Configuration des relations
        builder.HasOne(tgp => tgp.TermeGlossaire)
            .WithMany(tg => tg.AssociationsPolitiques)
            .HasForeignKey(tgp => tgp.TermeGlossaireId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(tgp => tgp.Politique)
            .WithMany(p => p.TermesGlossaire)
            .HasForeignKey(tgp => tgp.PolitiqueId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configuration des index
        builder.HasIndex(tgp => tgp.TermeGlossaireId)
            .HasDatabaseName("IX_TermesGlossairePolitiques_TermeGlossaireId");

        builder.HasIndex(tgp => tgp.PolitiqueId)
            .HasDatabaseName("IX_TermesGlossairePolitiques_PolitiqueId");

        builder.HasIndex(tgp => tgp.TypeApplication)
            .HasDatabaseName("IX_TermesGlossairePolitiques_TypeApplication");

        builder.HasIndex(tgp => tgp.NiveauImpact)
            .HasDatabaseName("IX_TermesGlossairePolitiques_NiveauImpact");

        builder.HasIndex(tgp => tgp.EstObligatoire)
            .HasDatabaseName("IX_TermesGlossairePolitiques_EstObligatoire");

        builder.HasIndex(tgp => tgp.EstActive)
            .HasDatabaseName("IX_TermesGlossairePolitiques_EstActive");

        builder.HasIndex(tgp => tgp.StatutConformite)
            .HasDatabaseName("IX_TermesGlossairePolitiques_StatutConformite");

        // Index unique para éviter les doublons
        builder.HasIndex(tgp => new { tgp.TermeGlossaireId, tgp.PolitiqueId })
            .IsUnique()
            .HasDatabaseName("UX_TermesGlossairePolitiques_TermePolitique");
    }
}
