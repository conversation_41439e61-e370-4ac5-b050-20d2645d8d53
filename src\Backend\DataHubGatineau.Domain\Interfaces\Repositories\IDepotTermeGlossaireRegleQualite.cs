using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Domain.Interfaces.Repositories;

/// <summary>
/// Interface du dépôt pour les associations entre termes du glossaire et règles de qualité.
/// </summary>
public interface IDepotTermeGlossaireRegleQualite : IDepotBase<TermeGlossaireRegleQualite>
{
    /// <summary>
    /// Obtient toutes les associations avec les entités liées.
    /// </summary>
    /// <returns>Collection d'associations avec les entités liées.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirTousAvecEntitesLieesAsync();

    /// <summary>
    /// Obtient une association par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association avec les entités liées si trouvée.</returns>
    Task<TermeGlossaireRegleQualite?> ObtenirParIdAvecEntitesLieesAsync(Guid id);

    /// <summary>
    /// Obtient toutes les associations pour un terme du glossaire spécifique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Collection d'associations pour le terme spécifié.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParTermeGlossaireAsync(Guid termeGlossaireId);

    /// <summary>
    /// Obtient toutes les associations pour une règle de qualité spécifique.
    /// </summary>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>Collection d'associations pour la règle spécifiée.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParRegleQualiteAsync(Guid regleQualiteId);

    /// <summary>
    /// Obtient les associations par type de validation.
    /// </summary>
    /// <param name="typeValidation">Type de validation.</param>
    /// <returns>Collection d'associations du type spécifié.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParTypeValidationAsync(string typeValidation);

    /// <summary>
    /// Obtient les associations actives.
    /// </summary>
    /// <returns>Collection d'associations actives.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirAssociationsActivesAsync();

    /// <summary>
    /// Obtient les associations obligatoires.
    /// </summary>
    /// <returns>Collection d'associations obligatoires.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirAssociationsObligatoiresAsync();

    /// <summary>
    /// Obtient les associations par niveau de priorité.
    /// </summary>
    /// <param name="priorite">Niveau de priorité (1=Haute, 2=Moyenne, 3=Basse).</param>
    /// <returns>Collection d'associations de la priorité spécifiée.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParPrioriteAsync(int priorite);

    /// <summary>
    /// Vérifie si une association existe déjà entre un terme et une règle de qualité.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>True si l'association existe, false sinon.</returns>
    Task<bool> AssociationExisteAsync(Guid termeGlossaireId, Guid regleQualiteId);

    /// <summary>
    /// Obtient les statistiques d'associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    Task<Dictionary<string, int>> ObtenirStatistiquesAsync();

    /// <summary>
    /// Obtient les associations avec seuil spécifique.
    /// </summary>
    /// <returns>Collection d'associations avec seuil personnalisé.</returns>
    Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirAvecSeuilSpecifiqueAsync();

    /// <summary>
    /// Supprime toutes les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    Task<int> SupprimerParTermeGlossaireAsync(Guid termeGlossaireId);

    /// <summary>
    /// Supprime toutes les associations pour une règle de qualité.
    /// </summary>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    Task<int> SupprimerParRegleQualiteAsync(Guid regleQualiteId);

    /// <summary>
    /// Active ou désactive une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="estActive">État d'activation.</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    Task<bool> ChangerEtatActivationAsync(Guid id, bool estActive);
}
