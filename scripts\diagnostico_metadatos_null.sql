-- Script de diagnóstico para identificar metadatos con TypeId NULL
-- Fecha: $(date)
-- Descripción: Identifica registros problemáticos en la tabla Metadonnees

USE [Gouvernance];
GO

PRINT '🔍 DIAGNÓSTICO: Metadatos con TypeId NULL';
PRINT '==========================================';

-- 1. Verificar metadatos con TypeId NULL
PRINT '';
PRINT '📊 METADATOS CON TypeId NULL:';
PRINT '------------------------------';

SELECT 
    m.Id as MetadonneeId,
    m.ActifDonneesId,
    m.Nom as MetadonneeNom,
    m.<PERSON>ur,
    m.TypeId,
    m.Type as TypeLegacy,
    a.Nom as ActifNom
FROM [Metadonnees].[Metadonnees] m
LEFT JOIN [Metadonnees].[ActifsDonnees] a ON m.ActifDonneesId = a.Id
WHERE m.TypeId IS NULL
ORDER BY m.ActifDonneesId, m.Nom;

-- 2. Contar metadatos por activo
PRINT '';
PRINT '📈 RESUMEN POR ACTIVO:';
PRINT '----------------------';

SELECT 
    a.Id as ActifId,
    a.Nom as ActifNom,
    COUNT(m.Id) as TotalMetadatos,
    SUM(CASE WHEN m.TypeId IS NULL THEN 1 ELSE 0 END) as MetadatosConTypeIdNull,
    SUM(CASE WHEN m.TypeId IS NOT NULL THEN 1 ELSE 0 END) as MetadatosConTypeIdValido
FROM [Metadonnees].[ActifsDonnees] a
LEFT JOIN [Metadonnees].[Metadonnees] m ON a.Id = m.ActifDonneesId
GROUP BY a.Id, a.Nom
HAVING COUNT(m.Id) > 0
ORDER BY MetadatosConTypeIdNull DESC, a.Nom;

-- 3. Verificar tipos de metadatos disponibles
PRINT '';
PRINT '🏷️ TIPOS DE METADATOS DISPONIBLES:';
PRINT '----------------------------------';

SELECT 
    Id,
    Nom,
    Description
FROM [Metadonnees].[TypesMetadonnees]
ORDER BY Nom;

-- 4. Verificar el activo problemático específico
PRINT '';
PRINT '🚨 ACTIVO PROBLEMÁTICO (756ecb9e-3e1e-4826-b35c-898d629832ff):';
PRINT '----------------------------------------------------------------';

SELECT 
    m.Id as MetadonneeId,
    m.Nom as MetadonneeNom,
    m.Valeur,
    m.TypeId,
    m.Type as TypeLegacy,
    m.CategorieId,
    CASE 
        WHEN m.TypeId IS NULL THEN '❌ PROBLEMÁTICO'
        ELSE '✅ OK'
    END as Estado
FROM [Metadonnees].[Metadonnees] m
WHERE m.ActifDonneesId = '756ecb9e-3e1e-4826-b35c-898d629832ff'
ORDER BY m.Nom;

-- 5. Verificar el activo que funciona
PRINT '';
PRINT '✅ ACTIVO QUE FUNCIONA (0dc20cc0-35e5-4da1-8360-b6bcd7f9d7a9):';
PRINT '--------------------------------------------------------------';

SELECT 
    m.Id as MetadonneeId,
    m.Nom as MetadonneeNom,
    m.Valeur,
    m.TypeId,
    m.Type as TypeLegacy,
    m.CategorieId,
    CASE 
        WHEN m.TypeId IS NULL THEN '❌ PROBLEMÁTICO'
        ELSE '✅ OK'
    END as Estado
FROM [Metadonnees].[Metadonnees] m
WHERE m.ActifDonneesId = '0dc20cc0-35e5-4da1-8360-b6bcd7f9d7a9'
ORDER BY m.Nom;

PRINT '';
PRINT '✅ Diagnóstico completado!';
