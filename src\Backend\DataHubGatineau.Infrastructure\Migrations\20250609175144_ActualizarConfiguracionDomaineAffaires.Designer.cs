﻿// <auto-generated />
using System;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    [DbContext(typeof(CentreDonneesDbContext))]
    [Migration("20250609175144_ActualizarConfiguracionDomaineAffaires")]
    partial class ActualizarConfiguracionDomaineAffaires
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);

            modelBuilder.Entity("ActifDonneesProduitDonnees", b =>
                {
                    b.Property<Guid>("ActifsDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ProduitsDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ActifsDonneesId", "ProduitsDonneesId");

                    b.HasIndex("ProduitsDonneesId");

                    b.ToTable("ActifDonneesProduitDonnees");
                });

            modelBuilder.Entity("ActifsTermes", b =>
                {
                    b.Property<Guid>("ActifsDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("TermesGlossaireId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("ActifsDonneesId", "TermesGlossaireId");

                    b.HasIndex("TermesGlossaireId");

                    b.ToTable("ActifsTermes", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ActifDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CheminAcces")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<int>("ClassificationSensibilite")
                        .HasColumnType("int");

                    b.Property<Guid?>("ConnexionSourceDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDerniereMiseAJour")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid?>("DomaineGouvernanceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("EstElementCritique")
                        .HasColumnType("bit");

                    b.Property<Guid?>("FormatActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("FrequenceMiseAJourId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Proprietaire")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("SourceActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("StatutActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("TypeActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ConnexionSourceDonneesId");

                    b.HasIndex("DomaineGouvernanceId");

                    b.HasIndex("FormatActifDonneesId");

                    b.HasIndex("FrequenceMiseAJourId");

                    b.HasIndex("SourceActifDonneesId");

                    b.HasIndex("StatutActifDonneesId");

                    b.HasIndex("TypeActifDonneesId");

                    b.ToTable("ActifsDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.CategorieMetadonnee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TypeMetadonneeId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("TypeMetadonneeId");

                    b.ToTable("CategoriesMetadonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.CategorieTypeActifDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Couleur")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("Icone")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Ordre")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.HasKey("Id");

                    b.HasIndex("Nom")
                        .IsUnique();

                    b.ToTable("CategoriesTypesActifDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ConfigurationScan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ConnexionSourceDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDernierScan")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("DernierScanReussi")
                        .HasColumnType("bit");

                    b.Property<bool>("EffectuerEchantillonnage")
                        .HasColumnType("bit");

                    b.Property<bool>("EstActif")
                        .HasColumnType("bit");

                    b.Property<string>("FiltresExclusion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("FiltresInclusion")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Frequence")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<TimeSpan?>("HeureScan")
                        .HasColumnType("time");

                    b.Property<bool>("IncluireMetadonneesTechniques")
                        .HasColumnType("bit");

                    b.Property<bool>("IncluireStatistiques")
                        .HasColumnType("bit");

                    b.Property<int?>("JourScan")
                        .HasColumnType("int");

                    b.Property<string>("MessageErreurDernierScan")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("MettreAJourLignage")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ConnexionSourceDonneesId");

                    b.ToTable("ConfigurationsScans", "Integration");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ConnexionSourceDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ChaineConnexion")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDernierTest")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool?>("DernierTestReussi")
                        .HasColumnType("bit");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TypeSource")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("ConnexionsSourceDonnees", "Integration");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.DomaineAffaires", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Code")
                        .HasMaxLength(10)
                        .HasColumnType("nvarchar(10)");

                    b.Property<string>("Couleur")
                        .HasMaxLength(7)
                        .HasColumnType("nvarchar(7)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActif")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Ordre")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("DomainesAffaires", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.DomaineGouvernance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Proprietaire")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("DomainesGouvernance", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.EtapeWorkflowApprobation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ApprobateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Commentaires")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateApprobation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Ordre")
                        .HasColumnType("int");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("WorkflowApprobationId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ApprobateurId");

                    b.HasIndex("WorkflowApprobationId");

                    b.ToTable("EtapesWorkflowApprobation", "Workflow");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.FormatActifDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("FormatsActifDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.FrequenceMiseAJour", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("FrequencesMiseAJour", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.GrapheLineage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActifRacineId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateDerniereMiseAJour")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("DonneesGraphe")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstValide")
                        .HasColumnType("bit");

                    b.Property<string>("MetadonneesGraphe")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<int>("NombreAretes")
                        .HasColumnType("int");

                    b.Property<int>("NombreNoeuds")
                        .HasColumnType("int");

                    b.Property<int>("ProfondeurMax")
                        .HasColumnType("int");

                    b.Property<long>("TempsCalculMs")
                        .HasColumnType("bigint");

                    b.Property<string>("TypeGraphe")
                        .IsRequired()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("Version")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ActifRacineId");

                    b.ToTable("GraphesLineage", "Lineage");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.HistoriqueScan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("ActifsDecouverts")
                        .HasColumnType("int");

                    b.Property<Guid>("ConfigurationScanId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateDebut")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstReussi")
                        .HasColumnType("bit");

                    b.Property<string>("MessageErreur")
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("MetadonneesCreees")
                        .HasColumnType("int");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NombreElementsAjoutes")
                        .HasColumnType("int");

                    b.Property<int>("NombreElementsModifies")
                        .HasColumnType("int");

                    b.Property<int>("NombreElementsScanes")
                        .HasColumnType("int");

                    b.Property<int>("NombreElementsSupprimes")
                        .HasColumnType("int");

                    b.Property<int>("StatistiquesGenerees")
                        .HasColumnType("int");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ConfigurationScanId");

                    b.ToTable("HistoriquesScans", "Integration");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.DemandeAcces", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ApprobateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CommentairesApprobateur")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateDebut")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDecision")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateFin")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("DemandeurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Justification")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RessourceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<string>("TypeRessource")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ActifDonneesId");

                    b.HasIndex("ApprobateurId");

                    b.HasIndex("DemandeurId");

                    b.HasIndex("RoleId");

                    b.ToTable("DemandesAcces", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Groupe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EstSysteme")
                        .HasColumnType("bit");

                    b.Property<string>("FournisseurIdentite")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("GroupeParentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("IdentifiantExterne")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("GroupeParentId");

                    b.ToTable("Groupes", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.GroupeRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("GroupeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RessourceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeRessource")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ActifDonneesId");

                    b.HasIndex("GroupeId");

                    b.HasIndex("RoleId");

                    b.ToTable("GroupesRoles", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.JournalAcces", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("AdresseIP")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("AgentUtilisateur")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateHeure")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("DetailsAction")
                        .HasColumnType("nvarchar(max)");

                    b.Property<bool>("EstAutorise")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("RaisonRefus")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<Guid?>("RessourceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TypeAction")
                        .HasColumnType("int");

                    b.Property<string>("TypeRessource")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("ActifDonneesId");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("JournalAcces", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Permission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Categorie")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EstSysteme")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("TypeLicence")
                        .HasColumnType("int");

                    b.Property<int>("TypePermission")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Permissions", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<bool>("EstSysteme")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NiveauHierarchique")
                        .HasColumnType("int");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("RoleParentId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("TypeRole")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("RoleParentId");

                    b.ToTable("Roles", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.RolePermission", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("PermissionId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("PermissionId");

                    b.HasIndex("RoleId");

                    b.ToTable("RolesPermissions", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Utilisateur", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CheminAvatar")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateVerrouillage")
                        .HasColumnType("datetime2");

                    b.Property<string>("Departement")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("DerniereConnexion")
                        .HasColumnType("datetime2");

                    b.Property<bool>("DeuxFacteursActive")
                        .HasColumnType("bit");

                    b.Property<string>("DeuxFacteursSecret")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<bool>("EstActif")
                        .HasColumnType("bit");

                    b.Property<string>("FournisseurIdentite")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("IdentifiantExterne")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MotDePasseHash")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MotDePasseSel")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("NomUtilisateur")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Prenom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Telephone")
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)");

                    b.Property<int>("TentativesConnexionEchouees")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("Utilisateurs", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.UtilisateurGroupe", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("GroupeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("GroupeId");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("UtilisateursGroupes", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.UtilisateurRole", b =>
                {
                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid?>("RessourceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeRessource")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("UtilisateurId", "RoleId");

                    b.HasIndex("ActifDonneesId");

                    b.HasIndex("RoleId");

                    b.ToTable("UtilisateursRoles", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.LignageDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("ElementsCiblesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ElementsSourcesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("TypeRelation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ElementsCiblesId");

                    b.HasIndex("ElementsSourcesId");

                    b.ToTable("LignageDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Metadonnee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CategorieId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("TypeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Valeur")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("ActifDonneesId");

                    b.HasIndex("CategorieId");

                    b.HasIndex("TypeId");

                    b.ToTable("Metadonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Politique", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Approbateur")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Categorie")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Code")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Contenu")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateApprobation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEntreeVigueur")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateProchaineRevision")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MotsCles")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("NiveauApplication")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid?>("PolitiqueParenteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Proprietaire")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Titre")
                        .IsRequired()
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("1.0");

                    b.HasKey("Id");

                    b.HasIndex("Categorie")
                        .HasDatabaseName("IX_Politiques_Categorie");

                    b.HasIndex("Code")
                        .IsUnique()
                        .HasDatabaseName("UX_Politiques_Code");

                    b.HasIndex("DateEntreeVigueur")
                        .HasDatabaseName("IX_Politiques_DateEntreeVigueur");

                    b.HasIndex("DateExpiration")
                        .HasDatabaseName("IX_Politiques_DateExpiration");

                    b.HasIndex("EstActive")
                        .HasDatabaseName("IX_Politiques_EstActive");

                    b.HasIndex("NiveauApplication")
                        .HasDatabaseName("IX_Politiques_NiveauApplication");

                    b.HasIndex("PolitiqueParenteId")
                        .HasDatabaseName("IX_Politiques_PolitiqueParenteId");

                    b.HasIndex("Statut")
                        .HasDatabaseName("IX_Politiques_Statut");

                    b.ToTable("Politiques", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.PolitiqueAcces", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpiration")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NiveauAcces")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Proprietaire")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ReglesAcces")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.HasKey("Id");

                    b.ToTable("PolitiquesAcces", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ProduitDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid>("DomaineGouvernanceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Proprietaire")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("DomaineGouvernanceId");

                    b.ToTable("ProduitsDonnees", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.RegleQualite", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ActionCorrectrice")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Champ")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DerniereExecution")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<bool>("ExecutionAutomatique")
                        .HasColumnType("bit");

                    b.Property<string>("Expression")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<string>("MessageErreur")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<double>("Seuil")
                        .HasColumnType("float");

                    b.Property<int>("Severite")
                        .HasColumnType("int");

                    b.Property<int>("Type")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.HasIndex("ActifDonneesId");

                    b.ToTable("ReglesQualite", "QualiteDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.RelationLineage", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActifCibleId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActifSourceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDerniereValidation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateValidation")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActive")
                        .HasColumnType("bit");

                    b.Property<bool>("EstDetectionAutomatique")
                        .HasColumnType("bit");

                    b.Property<bool>("EstValide")
                        .HasColumnType("bit");

                    b.Property<string>("MetadonneesRelation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MethodeDetection")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("NiveauConfiance")
                        .HasColumnType("int");

                    b.Property<string>("TypeRelation")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ValidePar")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("ActifCibleId");

                    b.HasIndex("ActifSourceId");

                    b.ToTable("RelationsLineage", "Lineage");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ResultatRegleQualite", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateExecution")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Message")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("RegleQualiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Statut")
                        .HasColumnType("int");

                    b.Property<double>("ValeurMesuree")
                        .HasColumnType("float");

                    b.HasKey("Id");

                    b.HasIndex("RegleQualiteId");

                    b.ToTable("ResultatsRegleQualite", "QualiteDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.SourceActifDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("SourcesActifDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.StatutActifDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("Ordre")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("StatutsActifDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaire", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Definition")
                        .IsRequired()
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid?>("DomaineAffairesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("DomaineGouvernanceId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("EstAssosiePolitiqueAcces")
                        .HasColumnType("bit");

                    b.Property<string>("Exemples")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<Guid?>("PolitiqueAccesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Proprietaire")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Synonymes")
                        .IsRequired()
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<Guid?>("TermeParentId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("DomaineAffairesId");

                    b.HasIndex("DomaineGouvernanceId");

                    b.HasIndex("PolitiqueAccesId");

                    b.HasIndex("TermeParentId");

                    b.ToTable("TermesGlossaire", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaireMetadonnee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<decimal?>("ConfianceScore")
                        .HasPrecision(3, 2)
                        .HasColumnType("decimal(3,2)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateAssociation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstValidee")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<Guid>("MetadonneeId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("TermeGlossaireId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeAssociation")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasDefaultValue("Manuel");

                    b.Property<string>("UtilisateurAssociation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("DateAssociation")
                        .HasDatabaseName("IX_TermesGlossaireMetadonnees_DateAssociation");

                    b.HasIndex("MetadonneeId")
                        .HasDatabaseName("IX_TermesGlossaireMetadonnees_MetadonneeId");

                    b.HasIndex("TermeGlossaireId")
                        .HasDatabaseName("IX_TermesGlossaireMetadonnees_TermeGlossaireId");

                    b.HasIndex("TypeAssociation")
                        .HasDatabaseName("IX_TermesGlossaireMetadonnees_TypeAssociation");

                    b.HasIndex("TermeGlossaireId", "MetadonneeId")
                        .IsUnique()
                        .HasDatabaseName("UX_TermesGlossaireMetadonnees_TermeMetadonnee");

                    b.ToTable("TermesGlossaireMetadonnees", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossairePolitique", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateAssociation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateDerniereEvaluation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateEntreeVigueurSpecifique")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateExpirationSpecifique")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("EstObligatoire")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NiveauImpact")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("Moyen");

                    b.Property<string>("ParametresSpecifiques")
                        .HasMaxLength(1000)
                        .HasColumnType("nvarchar(1000)");

                    b.Property<Guid>("PolitiqueId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<int>("Priorite")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(3);

                    b.Property<decimal?>("ScoreConformite")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<string>("StatutConformite")
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<Guid>("TermeGlossaireId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeApplication")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasDefaultValue("Classification");

                    b.Property<string>("UtilisateurAssociation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("EstActive")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_EstActive");

                    b.HasIndex("EstObligatoire")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_EstObligatoire");

                    b.HasIndex("NiveauImpact")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_NiveauImpact");

                    b.HasIndex("PolitiqueId")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_PolitiqueId");

                    b.HasIndex("StatutConformite")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_StatutConformite");

                    b.HasIndex("TermeGlossaireId")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_TermeGlossaireId");

                    b.HasIndex("TypeApplication")
                        .HasDatabaseName("IX_TermesGlossairePolitiques_TypeApplication");

                    b.HasIndex("TermeGlossaireId", "PolitiqueId")
                        .IsUnique()
                        .HasDatabaseName("UX_TermesGlossairePolitiques_TermePolitique");

                    b.ToTable("TermesGlossairePolitiques", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaireRegleQualite", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Commentaires")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateAssociation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstActive")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<bool>("EstObligatoire")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<int>("Priorite")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(3);

                    b.Property<Guid>("RegleQualiteId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<decimal?>("SeuilSpecifique")
                        .HasPrecision(5, 2)
                        .HasColumnType("decimal(5,2)");

                    b.Property<Guid>("TermeGlossaireId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeValidation")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)")
                        .HasDefaultValue("Conformite");

                    b.Property<string>("UtilisateurAssociation")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.HasIndex("EstActive")
                        .HasDatabaseName("IX_TermesGlossaireReglesQualite_EstActive");

                    b.HasIndex("EstObligatoire")
                        .HasDatabaseName("IX_TermesGlossaireReglesQualite_EstObligatoire");

                    b.HasIndex("RegleQualiteId")
                        .HasDatabaseName("IX_TermesGlossaireReglesQualite_RegleQualiteId");

                    b.HasIndex("TermeGlossaireId")
                        .HasDatabaseName("IX_TermesGlossaireReglesQualite_TermeGlossaireId");

                    b.HasIndex("TypeValidation")
                        .HasDatabaseName("IX_TermesGlossaireReglesQualite_TypeValidation");

                    b.HasIndex("TermeGlossaireId", "RegleQualiteId")
                        .IsUnique()
                        .HasDatabaseName("UX_TermesGlossaireReglesQualite_TermeRegle");

                    b.ToTable("TermesGlossaireReglesQualite", "Gouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TypeActifDonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid?>("CategorieId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(255)
                        .HasColumnType("nvarchar(255)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("CategorieId");

                    b.HasIndex("Nom")
                        .IsUnique();

                    b.ToTable("TypesActifDonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TypeMetadonnee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.HasKey("Id");

                    b.ToTable("TypesMetadonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.WorkflowApprobation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("ActifDonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime?>("DateApprobationFinale")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("DateSoumission")
                        .HasColumnType("datetime2");

                    b.Property<Guid>("InitiateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Statut")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.HasIndex("ActifDonneesId");

                    b.HasIndex("InitiateurId");

                    b.ToTable("WorkflowsApprobation", "Workflow");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.JetonRafraichissementJWT", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<bool>("EstRevoque")
                        .HasColumnType("bit");

                    b.Property<DateTime>("Expiration")
                        .HasColumnType("datetime2");

                    b.Property<string>("Jeton")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("Id");

                    b.HasIndex("UtilisateurId");

                    b.ToTable("JetonsRafraichissementJWT", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.RoleJWT", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Description")
                        .HasMaxLength(200)
                        .HasColumnType("nvarchar(200)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.HasKey("Id");

                    b.ToTable("RolesJWT", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.UtilisateurJWT", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<bool>("AuthenticationDeuxFacteursActive")
                        .HasColumnType("bit");

                    b.Property<string>("CleSecreteDeuxFacteurs")
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .HasColumnType("datetime2");

                    b.Property<DateTime>("DateModification")
                        .HasColumnType("datetime2");

                    b.Property<string>("Email")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<DateTime?>("ExpirationJetonReinitialisationMotDePasse")
                        .HasColumnType("datetime2");

                    b.Property<DateTime?>("FinVerrouillage")
                        .HasColumnType("datetime2");

                    b.Property<string>("JetonReinitialisationMotDePasse")
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("MotDePasseHash")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("NomUtilisateur")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<int>("TentativesConnexionEchouees")
                        .HasColumnType("int");

                    b.HasKey("Id");

                    b.ToTable("UtilisateursJWT", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.UtilisateurRoleJWT", b =>
                {
                    b.Property<Guid>("UtilisateurId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier");

                    b.HasKey("UtilisateurId", "RoleId");

                    b.HasIndex("RoleId");

                    b.ToTable("UtilisateursRolesJWT", "Identite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Metadata.DefinitionMetadonnee", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateModification")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstCalcule")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<bool>("EstObligatoire")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(false);

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<int>("Ordre")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasDefaultValue(0);

                    b.Property<string>("ReglesValidation")
                        .HasColumnType("nvarchar(max)");

                    b.Property<Guid>("SchemaMetadonneesId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("TypeDonnee")
                        .IsRequired()
                        .HasMaxLength(50)
                        .HasColumnType("nvarchar(50)");

                    b.Property<string>("ValeurParDefaut")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.HasKey("Id");

                    b.HasIndex("Ordre")
                        .HasDatabaseName("IX_DefinitionsMetadonnees_Ordre");

                    b.HasIndex("SchemaMetadonneesId")
                        .HasDatabaseName("IX_DefinitionsMetadonnees_SchemaMetadonneesId");

                    b.HasIndex("SchemaMetadonneesId", "Ordre")
                        .HasDatabaseName("IX_DefinitionsMetadonnees_SchemaMetadonneesId_Ordre");

                    b.ToTable("DefinitionsMetadonnees", "Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Metadata.SchemaMetadonnees", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("CreePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<DateTime>("DateCreation")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<DateTime>("DateModification")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("datetime2")
                        .HasDefaultValueSql("GETUTCDATE()");

                    b.Property<string>("Description")
                        .HasMaxLength(500)
                        .HasColumnType("nvarchar(500)");

                    b.Property<bool>("EstActif")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("bit")
                        .HasDefaultValue(true);

                    b.Property<string>("ModifiePar")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)");

                    b.Property<string>("Nom")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("TypeActif")
                        .IsRequired()
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)");

                    b.Property<string>("Version")
                        .IsRequired()
                        .ValueGeneratedOnAdd()
                        .HasMaxLength(20)
                        .HasColumnType("nvarchar(20)")
                        .HasDefaultValue("1.0");

                    b.HasKey("Id");

                    b.HasIndex("EstActif")
                        .HasDatabaseName("IX_SchemasMetadonnees_EstActif");

                    b.HasIndex("TypeActif")
                        .HasDatabaseName("IX_SchemasMetadonnees_TypeActif");

                    b.HasIndex("TypeActif", "EstActif")
                        .IsUnique()
                        .HasDatabaseName("IX_SchemasMetadonnees_TypeActif_EstActif_Unique")
                        .HasFilter("EstActif = 1");

                    b.ToTable("SchemasMetadonnees", "Metadonnees");
                });

            modelBuilder.Entity("ActifDonneesProduitDonnees", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", null)
                        .WithMany()
                        .HasForeignKey("ActifsDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.ProduitDonnees", null)
                        .WithMany()
                        .HasForeignKey("ProduitsDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("ActifsTermes", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", null)
                        .WithMany()
                        .HasForeignKey("ActifsDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.TermeGlossaire", null)
                        .WithMany()
                        .HasForeignKey("TermesGlossaireId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ActifDonnees", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ConnexionSourceDonnees", "ConnexionSourceDonnees")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("ConnexionSourceDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.DomaineGouvernance", "DomaineGouvernance")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("DomaineGouvernanceId");

                    b.HasOne("DataHubGatineau.Domain.Entites.FormatActifDonnees", "FormatActifDonnees")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("FormatActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.FrequenceMiseAJour", "FrequenceMiseAJour")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("FrequenceMiseAJourId");

                    b.HasOne("DataHubGatineau.Domain.Entites.SourceActifDonnees", "SourceActifDonnees")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("SourceActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.StatutActifDonnees", "StatutActifDonnees")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("StatutActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.TypeActifDonnees", "TypeActifDonnees")
                        .WithMany("ActifsDonnees")
                        .HasForeignKey("TypeActifDonneesId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ConnexionSourceDonnees");

                    b.Navigation("DomaineGouvernance");

                    b.Navigation("FormatActifDonnees");

                    b.Navigation("FrequenceMiseAJour");

                    b.Navigation("SourceActifDonnees");

                    b.Navigation("StatutActifDonnees");

                    b.Navigation("TypeActifDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.CategorieMetadonnee", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.TypeMetadonnee", "TypeMetadonnee")
                        .WithMany("Categories")
                        .HasForeignKey("TypeMetadonneeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("TypeMetadonnee");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ConfigurationScan", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ConnexionSourceDonnees", "ConnexionSourceDonnees")
                        .WithMany()
                        .HasForeignKey("ConnexionSourceDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ConnexionSourceDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.EtapeWorkflowApprobation", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Approbateur")
                        .WithMany()
                        .HasForeignKey("ApprobateurId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.WorkflowApprobation", "WorkflowApprobation")
                        .WithMany("Etapes")
                        .HasForeignKey("WorkflowApprobationId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Approbateur");

                    b.Navigation("WorkflowApprobation");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.GrapheLineage", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifRacine")
                        .WithMany()
                        .HasForeignKey("ActifRacineId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ActifRacine");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.HistoriqueScan", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ConfigurationScan", "ConfigurationScan")
                        .WithMany("HistoriqueScans")
                        .HasForeignKey("ConfigurationScanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ConfigurationScan");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.DemandeAcces", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany()
                        .HasForeignKey("ActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Approbateur")
                        .WithMany()
                        .HasForeignKey("ApprobateurId");

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Demandeur")
                        .WithMany()
                        .HasForeignKey("DemandeurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId");

                    b.Navigation("ActifDonnees");

                    b.Navigation("Approbateur");

                    b.Navigation("Demandeur");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Groupe", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Groupe", "GroupeParent")
                        .WithMany("GroupesEnfants")
                        .HasForeignKey("GroupeParentId");

                    b.Navigation("GroupeParent");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.GroupeRole", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany()
                        .HasForeignKey("ActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Groupe", "Groupe")
                        .WithMany("GroupeRoles")
                        .HasForeignKey("GroupeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Role", "Role")
                        .WithMany("GroupeRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActifDonnees");

                    b.Navigation("Groupe");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.JournalAcces", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany()
                        .HasForeignKey("ActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Utilisateur")
                        .WithMany()
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActifDonnees");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Role", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Role", "RoleParent")
                        .WithMany("RolesEnfants")
                        .HasForeignKey("RoleParentId");

                    b.Navigation("RoleParent");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.RolePermission", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Permission", "Permission")
                        .WithMany("RolePermissions")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Role", "Role")
                        .WithMany("RolePermissions")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("Role");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.UtilisateurGroupe", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Groupe", "Groupe")
                        .WithMany("UtilisateurGroupes")
                        .HasForeignKey("GroupeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Utilisateur")
                        .WithMany("UtilisateurGroupes")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Groupe");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.UtilisateurRole", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany()
                        .HasForeignKey("ActifDonneesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Role", "Role")
                        .WithMany("UtilisateurRoles")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Utilisateur")
                        .WithMany("UtilisateurRoles")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActifDonnees");

                    b.Navigation("Role");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.LignageDonnees", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ElementsCible")
                        .WithMany()
                        .HasForeignKey("ElementsCiblesId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ElementsSource")
                        .WithMany()
                        .HasForeignKey("ElementsSourcesId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ElementsCible");

                    b.Navigation("ElementsSource");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Metadonnee", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany("Metadonnees")
                        .HasForeignKey("ActifDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.CategorieMetadonnee", "CategorieMetadonnee")
                        .WithMany("Metadonnees")
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("DataHubGatineau.Domain.Entites.TypeMetadonnee", "TypeMetadonnee")
                        .WithMany("Metadonnees")
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("ActifDonnees");

                    b.Navigation("CategorieMetadonnee");

                    b.Navigation("TypeMetadonnee");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Politique", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Politique", "PolitiqueParente")
                        .WithMany("VersionsEnfants")
                        .HasForeignKey("PolitiqueParenteId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("PolitiqueParente");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ProduitDonnees", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.DomaineGouvernance", "DomaineGouvernance")
                        .WithMany()
                        .HasForeignKey("DomaineGouvernanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("DomaineGouvernance");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.RegleQualite", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany("ReglesQualite")
                        .HasForeignKey("ActifDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActifDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.RelationLineage", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifCible")
                        .WithMany()
                        .HasForeignKey("ActifCibleId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifSource")
                        .WithMany()
                        .HasForeignKey("ActifSourceId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("ActifCible");

                    b.Navigation("ActifSource");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ResultatRegleQualite", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.RegleQualite", "RegleQualite")
                        .WithMany("Resultats")
                        .HasForeignKey("RegleQualiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RegleQualite");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaire", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.DomaineAffaires", "DomaineAffaires")
                        .WithMany("TermesGlossaire")
                        .HasForeignKey("DomaineAffairesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.DomaineGouvernance", "DomaineGouvernance")
                        .WithMany("TermesGlossaire")
                        .HasForeignKey("DomaineGouvernanceId");

                    b.HasOne("DataHubGatineau.Domain.Entites.PolitiqueAcces", "PolitiqueAcces")
                        .WithMany("TermesGlossaire")
                        .HasForeignKey("PolitiqueAccesId");

                    b.HasOne("DataHubGatineau.Domain.Entites.TermeGlossaire", "TermeParent")
                        .WithMany("TermesEnfants")
                        .HasForeignKey("TermeParentId")
                        .OnDelete(DeleteBehavior.Restrict);

                    b.Navigation("DomaineAffaires");

                    b.Navigation("DomaineGouvernance");

                    b.Navigation("PolitiqueAcces");

                    b.Navigation("TermeParent");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaireMetadonnee", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Metadonnee", "Metadonnee")
                        .WithMany("AssociationsTermesGlossaire")
                        .HasForeignKey("MetadonneeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.TermeGlossaire", "TermeGlossaire")
                        .WithMany("AssociationsMetadonnees")
                        .HasForeignKey("TermeGlossaireId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Metadonnee");

                    b.Navigation("TermeGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossairePolitique", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.Politique", "Politique")
                        .WithMany("TermesGlossaire")
                        .HasForeignKey("PolitiqueId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.TermeGlossaire", "TermeGlossaire")
                        .WithMany("AssociationsPolitiques")
                        .HasForeignKey("TermeGlossaireId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Politique");

                    b.Navigation("TermeGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaireRegleQualite", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.RegleQualite", "RegleQualite")
                        .WithMany("AssociationsTermesGlossaire")
                        .HasForeignKey("RegleQualiteId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.TermeGlossaire", "TermeGlossaire")
                        .WithMany("AssociationsReglesQualite")
                        .HasForeignKey("TermeGlossaireId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("RegleQualite");

                    b.Navigation("TermeGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TypeActifDonnees", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.CategorieTypeActifDonnees", "Categorie")
                        .WithMany("TypesActifDonnees")
                        .HasForeignKey("CategorieId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.Navigation("Categorie");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.WorkflowApprobation", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entites.ActifDonnees", "ActifDonnees")
                        .WithMany("WorkflowsApprobation")
                        .HasForeignKey("ActifDonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entites.Identity.Utilisateur", "Initiateur")
                        .WithMany()
                        .HasForeignKey("InitiateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ActifDonnees");

                    b.Navigation("Initiateur");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.JetonRafraichissementJWT", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entities.Identity.UtilisateurJWT", "Utilisateur")
                        .WithMany("JetonsRafraichissement")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.UtilisateurRoleJWT", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entities.Identity.RoleJWT", "Role")
                        .WithMany("Utilisateurs")
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("DataHubGatineau.Domain.Entities.Identity.UtilisateurJWT", "Utilisateur")
                        .WithMany("Roles")
                        .HasForeignKey("UtilisateurId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");

                    b.Navigation("Utilisateur");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Metadata.DefinitionMetadonnee", b =>
                {
                    b.HasOne("DataHubGatineau.Domain.Entities.Metadata.SchemaMetadonnees", "SchemaMetadonnees")
                        .WithMany("DefinitionsMetadonnees")
                        .HasForeignKey("SchemaMetadonneesId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("SchemaMetadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ActifDonnees", b =>
                {
                    b.Navigation("Metadonnees");

                    b.Navigation("ReglesQualite");

                    b.Navigation("WorkflowsApprobation");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.CategorieMetadonnee", b =>
                {
                    b.Navigation("Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.CategorieTypeActifDonnees", b =>
                {
                    b.Navigation("TypesActifDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ConfigurationScan", b =>
                {
                    b.Navigation("HistoriqueScans");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.ConnexionSourceDonnees", b =>
                {
                    b.Navigation("ActifsDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.DomaineAffaires", b =>
                {
                    b.Navigation("TermesGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.DomaineGouvernance", b =>
                {
                    b.Navigation("ActifsDonnees");

                    b.Navigation("TermesGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.FormatActifDonnees", b =>
                {
                    b.Navigation("ActifsDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.FrequenceMiseAJour", b =>
                {
                    b.Navigation("ActifsDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Groupe", b =>
                {
                    b.Navigation("GroupeRoles");

                    b.Navigation("GroupesEnfants");

                    b.Navigation("UtilisateurGroupes");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Permission", b =>
                {
                    b.Navigation("RolePermissions");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Role", b =>
                {
                    b.Navigation("GroupeRoles");

                    b.Navigation("RolePermissions");

                    b.Navigation("RolesEnfants");

                    b.Navigation("UtilisateurRoles");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Identity.Utilisateur", b =>
                {
                    b.Navigation("UtilisateurGroupes");

                    b.Navigation("UtilisateurRoles");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Metadonnee", b =>
                {
                    b.Navigation("AssociationsTermesGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.Politique", b =>
                {
                    b.Navigation("TermesGlossaire");

                    b.Navigation("VersionsEnfants");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.PolitiqueAcces", b =>
                {
                    b.Navigation("TermesGlossaire");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.RegleQualite", b =>
                {
                    b.Navigation("AssociationsTermesGlossaire");

                    b.Navigation("Resultats");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.SourceActifDonnees", b =>
                {
                    b.Navigation("ActifsDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.StatutActifDonnees", b =>
                {
                    b.Navigation("ActifsDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TermeGlossaire", b =>
                {
                    b.Navigation("AssociationsMetadonnees");

                    b.Navigation("AssociationsPolitiques");

                    b.Navigation("AssociationsReglesQualite");

                    b.Navigation("TermesEnfants");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TypeActifDonnees", b =>
                {
                    b.Navigation("ActifsDonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.TypeMetadonnee", b =>
                {
                    b.Navigation("Categories");

                    b.Navigation("Metadonnees");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entites.WorkflowApprobation", b =>
                {
                    b.Navigation("Etapes");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.RoleJWT", b =>
                {
                    b.Navigation("Utilisateurs");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Identity.UtilisateurJWT", b =>
                {
                    b.Navigation("JetonsRafraichissement");

                    b.Navigation("Roles");
                });

            modelBuilder.Entity("DataHubGatineau.Domain.Entities.Metadata.SchemaMetadonnees", b =>
                {
                    b.Navigation("DefinitionsMetadonnees");
                });
#pragma warning restore 612, 618
        }
    }
}
