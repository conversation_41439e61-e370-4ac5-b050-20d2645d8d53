-- Script pour créer des données de test pour les schémas de métadonnées
-- Ce script crée des schémas de métadonnées pour différents types d'actifs

USE Gouvernance;
GO

-- Configuration requise pour les tables avec index
SET QUOTED_IDENTIFIER ON;
SET ANSI_NULLS ON;
GO

-- Insérer un schéma de métadonnées pour le type "Table"
DECLARE @SchemaTableId UNIQUEIDENTIFIER = NEWID();

INSERT INTO [Metadonnees].[SchemasMetadonnees] (
    [Id],
    [Nom],
    [Description],
    [TypeActif],
    [Version],
    [EstActif],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES (
    @SchemaTableId,
    'Schéma Table Standard',
    'Schéma de métadonnées standard pour les tables de base de données',
    'Table',
    '1.0',
    1, -- EstActif = true
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

-- Insérer les définitions de métadonnées pour le schéma Table
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id],
    [SchemaMetadonneesId],
    [Nom],
    [Description],
    [TypeDonnee],
    [EstObligatoire],
    [EstCalcule],
    [Ordre],
    [ValeurParDefaut],
    [ReglesValidation],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES 
(
    NEWID(),
    @SchemaTableId,
    'Nombre de lignes',
    'Nombre total de lignes dans la table',
    'Entier',
    1, -- Obligatoire
    0, -- Pas calculé
    1, -- Ordre
    '0',
    'min:0',
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaTableId,
    'Nombre de colonnes',
    'Nombre total de colonnes dans la table',
    'Entier',
    1, -- Obligatoire
    0, -- Pas calculé
    2, -- Ordre
    '0',
    'min:1',
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaTableId,
    'Taille en Mo',
    'Taille de la table en mégaoctets',
    'Decimal',
    0, -- Pas obligatoire
    0, -- Pas calculé
    3, -- Ordre
    '0.0',
    'min:0',
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaTableId,
    'Date dernière analyse',
    'Date de la dernière analyse de la table',
    'Date',
    0, -- Pas obligatoire
    1, -- Calculé automatiquement
    4, -- Ordre
    NULL,
    NULL,
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaTableId,
    'Index principaux',
    'Liste des index principaux de la table',
    'Texte',
    0, -- Pas obligatoire
    0, -- Pas calculé
    5, -- Ordre
    '',
    NULL,
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

-- Insérer un schéma de métadonnées pour le type "Column"
DECLARE @SchemaColumnId UNIQUEIDENTIFIER = NEWID();

INSERT INTO [Metadonnees].[SchemasMetadonnees] (
    [Id],
    [Nom],
    [Description],
    [TypeActif],
    [Version],
    [EstActif],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES (
    @SchemaColumnId,
    'Schéma Colonne Standard',
    'Schéma de métadonnées standard pour les colonnes de base de données',
    'Column',
    '1.0',
    1, -- EstActif = true
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

-- Insérer les définitions de métadonnées pour le schéma Column
INSERT INTO [Metadonnees].[DefinitionsMetadonnees] (
    [Id],
    [SchemaMetadonneesId],
    [Nom],
    [Description],
    [TypeDonnee],
    [EstObligatoire],
    [EstCalcule],
    [Ordre],
    [ValeurParDefaut],
    [ReglesValidation],
    [CreePar],
    [DateCreation],
    [ModifiePar],
    [DateModification]
) VALUES 
(
    NEWID(),
    @SchemaColumnId,
    'Type de données',
    'Type de données de la colonne (VARCHAR, INT, etc.)',
    'Texte',
    1, -- Obligatoire
    0, -- Pas calculé
    1, -- Ordre
    'VARCHAR',
    'required',
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaColumnId,
    'Longueur maximale',
    'Longueur maximale pour les types de données texte',
    'Entier',
    0, -- Pas obligatoire
    0, -- Pas calculé
    2, -- Ordre
    '255',
    'min:1|max:8000',
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaColumnId,
    'Permet NULL',
    'Indique si la colonne peut contenir des valeurs NULL',
    'Booleen',
    1, -- Obligatoire
    0, -- Pas calculé
    3, -- Ordre
    'true',
    NULL,
    'System',
    GETDATE(),
    'System',
    GETDATE()
),
(
    NEWID(),
    @SchemaColumnId,
    'Valeur par défaut',
    'Valeur par défaut de la colonne',
    'Texte',
    0, -- Pas obligatoire
    0, -- Pas calculé
    4, -- Ordre
    '',
    NULL,
    'System',
    GETDATE(),
    'System',
    GETDATE()
);

PRINT 'Données de test pour les schémas de métadonnées créées avec succès!';
PRINT 'Schéma Table ID: ' + CAST(@SchemaTableId AS VARCHAR(36));
PRINT 'Schéma Column ID: ' + CAST(@SchemaColumnId AS VARCHAR(36));
