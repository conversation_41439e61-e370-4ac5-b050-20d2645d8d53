using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces.Repositories;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les associations entre termes du glossaire et règles de qualité.
/// </summary>
public class DepotTermeGlossaireRegleQualite : DepotBase<TermeGlossaireRegleQualite>, IDepotTermeGlossaireRegleQualite
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotTermeGlossaireRegleQualite"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotTermeGlossaireRegleQualite(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient toutes les associations avec les entités liées.
    /// </summary>
    /// <returns>Collection d'associations avec les entités liées.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirTousAvecEntitesLieesAsync()
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .OrderBy(tgrq => tgrq.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient une association par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association avec les entités liées si trouvée.</returns>
    public async Task<TermeGlossaireRegleQualite?> ObtenirParIdAvecEntitesLieesAsync(Guid id)
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .FirstOrDefaultAsync(tgrq => tgrq.Id == id);
    }

    /// <summary>
    /// Obtient toutes les associations pour un terme du glossaire spécifique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Collection d'associations pour le terme spécifié.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParTermeGlossaireAsync(Guid termeGlossaireId)
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.RegleQualite)
            .Where(tgrq => tgrq.TermeGlossaireId == termeGlossaireId)
            .OrderBy(tgrq => tgrq.Priorite)
            .ThenBy(tgrq => tgrq.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient toutes les associations pour une règle de qualité spécifique.
    /// </summary>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>Collection d'associations pour la règle spécifiée.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParRegleQualiteAsync(Guid regleQualiteId)
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Where(tgrq => tgrq.RegleQualiteId == regleQualiteId)
            .OrderBy(tgrq => tgrq.Priorite)
            .ThenBy(tgrq => tgrq.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par type de validation.
    /// </summary>
    /// <param name="typeValidation">Type de validation.</param>
    /// <returns>Collection d'associations du type spécifié.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParTypeValidationAsync(string typeValidation)
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .Where(tgrq => tgrq.TypeValidation == typeValidation)
            .OrderBy(tgrq => tgrq.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations actives.
    /// </summary>
    /// <returns>Collection d'associations actives.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirAssociationsActivesAsync()
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .Where(tgrq => tgrq.EstActive)
            .OrderBy(tgrq => tgrq.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations obligatoires.
    /// </summary>
    /// <returns>Collection d'associations obligatoires.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirAssociationsObligatoiresAsync()
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .Where(tgrq => tgrq.EstObligatoire)
            .OrderBy(tgrq => tgrq.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par niveau de priorité.
    /// </summary>
    /// <param name="priorite">Niveau de priorité (1=Haute, 2=Moyenne, 3=Basse).</param>
    /// <returns>Collection d'associations de la priorité spécifiée.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirParPrioriteAsync(int priorite)
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .Where(tgrq => tgrq.Priorite == priorite)
            .OrderBy(tgrq => tgrq.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si une association existe déjà entre un terme et une règle de qualité.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>True si l'association existe, false sinon.</returns>
    public async Task<bool> AssociationExisteAsync(Guid termeGlossaireId, Guid regleQualiteId)
    {
        return await _context.TermesGlossaireReglesQualite
            .AnyAsync(tgrq => tgrq.TermeGlossaireId == termeGlossaireId && 
                             tgrq.RegleQualiteId == regleQualiteId);
    }

    /// <summary>
    /// Obtient les statistiques d'associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    public async Task<Dictionary<string, int>> ObtenirStatistiquesAsync()
    {
        var stats = new Dictionary<string, int>();

        stats["Total"] = await _context.TermesGlossaireReglesQualite.CountAsync();
        stats["Actives"] = await _context.TermesGlossaireReglesQualite.CountAsync(tgrq => tgrq.EstActive);
        stats["Obligatoires"] = await _context.TermesGlossaireReglesQualite.CountAsync(tgrq => tgrq.EstObligatoire);
        stats["PrioriteHaute"] = await _context.TermesGlossaireReglesQualite.CountAsync(tgrq => tgrq.Priorite == 1);
        stats["PrioriteMoyenne"] = await _context.TermesGlossaireReglesQualite.CountAsync(tgrq => tgrq.Priorite == 2);
        stats["PrioriteBasse"] = await _context.TermesGlossaireReglesQualite.CountAsync(tgrq => tgrq.Priorite == 3);

        return stats;
    }

    /// <summary>
    /// Obtient les associations avec seuil spécifique.
    /// </summary>
    /// <returns>Collection d'associations avec seuil personnalisé.</returns>
    public async Task<IEnumerable<TermeGlossaireRegleQualite>> ObtenirAvecSeuilSpecifiqueAsync()
    {
        return await _context.TermesGlossaireReglesQualite
            .Include(tgrq => tgrq.TermeGlossaire)
            .Include(tgrq => tgrq.RegleQualite)
            .Where(tgrq => tgrq.SeuilSpecifique.HasValue)
            .OrderBy(tgrq => tgrq.SeuilSpecifique)
            .ToListAsync();
    }

    /// <summary>
    /// Supprime toutes les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    public async Task<int> SupprimerParTermeGlossaireAsync(Guid termeGlossaireId)
    {
        var associations = await _context.TermesGlossaireReglesQualite
            .Where(tgrq => tgrq.TermeGlossaireId == termeGlossaireId)
            .ToListAsync();

        _context.TermesGlossaireReglesQualite.RemoveRange(associations);
        await _context.SaveChangesAsync();

        return associations.Count;
    }

    /// <summary>
    /// Supprime toutes les associations pour une règle de qualité.
    /// </summary>
    /// <param name="regleQualiteId">Identifiant de la règle de qualité.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    public async Task<int> SupprimerParRegleQualiteAsync(Guid regleQualiteId)
    {
        var associations = await _context.TermesGlossaireReglesQualite
            .Where(tgrq => tgrq.RegleQualiteId == regleQualiteId)
            .ToListAsync();

        _context.TermesGlossaireReglesQualite.RemoveRange(associations);
        await _context.SaveChangesAsync();

        return associations.Count;
    }

    /// <summary>
    /// Active ou désactive une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="estActive">État d'activation.</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    public async Task<bool> ChangerEtatActivationAsync(Guid id, bool estActive)
    {
        var association = await _context.TermesGlossaireReglesQualite.FindAsync(id);
        if (association == null) return false;

        association.EstActive = estActive;
        association.DateModification = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();
        return true;
    }
}
