using DataHubGatineau.Domain.Entites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataHubGatineau.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration de l'entité TermeGlossaireMetadonnee pour Entity Framework Core.
/// </summary>
public class TermeGlossaireMetadonneeConfiguration : IEntityTypeConfiguration<TermeGlossaireMetadonnee>
{
    /// <summary>
    /// Configure l'entité TermeGlossaireMetadonnee.
    /// </summary>
    /// <param name="builder">Constructeur de type d'entité.</param>
    public void Configure(EntityTypeBuilder<TermeGlossaireMetadonnee> builder)
    {
        // Configuration de la table
        builder.ToTable("TermesGlossaireMetadonnees", "Gouvernance");

        // Configuration de la clé primaire
        builder.HasKey(tgm => tgm.Id);

        // Configuration des propriétés
        builder.Property(tgm => tgm.TermeGlossaireId)
            .IsRequired();

        builder.Property(tgm => tgm.MetadonneeId)
            .IsRequired();

        builder.Property(tgm => tgm.TypeAssociation)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("Manuel");

        builder.Property(tgm => tgm.UtilisateurAssociation)
            .HasMaxLength(100);

        builder.Property(tgm => tgm.DateAssociation)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(tgm => tgm.ConfianceScore)
            .HasPrecision(3, 2);

        builder.Property(tgm => tgm.EstValidee)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(tgm => tgm.Commentaires)
            .HasMaxLength(500);

        // Configuration des relations
        builder.HasOne(tgm => tgm.TermeGlossaire)
            .WithMany(tg => tg.AssociationsMetadonnees)
            .HasForeignKey(tgm => tgm.TermeGlossaireId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(tgm => tgm.Metadonnee)
            .WithMany(m => m.AssociationsTermesGlossaire)
            .HasForeignKey(tgm => tgm.MetadonneeId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configuration des index
        builder.HasIndex(tgm => tgm.TermeGlossaireId)
            .HasDatabaseName("IX_TermesGlossaireMetadonnees_TermeGlossaireId");

        builder.HasIndex(tgm => tgm.MetadonneeId)
            .HasDatabaseName("IX_TermesGlossaireMetadonnees_MetadonneeId");

        builder.HasIndex(tgm => tgm.TypeAssociation)
            .HasDatabaseName("IX_TermesGlossaireMetadonnees_TypeAssociation");

        builder.HasIndex(tgm => tgm.DateAssociation)
            .HasDatabaseName("IX_TermesGlossaireMetadonnees_DateAssociation");

        // Index unique pour éviter les doublons
        builder.HasIndex(tgm => new { tgm.TermeGlossaireId, tgm.MetadonneeId })
            .IsUnique()
            .HasDatabaseName("UX_TermesGlossaireMetadonnees_TermeMetadonnee");
    }
}
