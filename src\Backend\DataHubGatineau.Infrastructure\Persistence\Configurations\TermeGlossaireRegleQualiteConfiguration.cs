using DataHubGatineau.Domain.Entites;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;

namespace DataHubGatineau.Infrastructure.Persistence.Configurations;

/// <summary>
/// Configuration de l'entité TermeGlossaireRegleQualite pour Entity Framework Core.
/// </summary>
public class TermeGlossaireRegleQualiteConfiguration : IEntityTypeConfiguration<TermeGlossaireRegleQualite>
{
    /// <summary>
    /// Configure l'entité TermeGlossaireRegleQualite.
    /// </summary>
    /// <param name="builder">Constructeur de type d'entité.</param>
    public void Configure(EntityTypeBuilder<TermeGlossaireRegleQualite> builder)
    {
        // Configuration de la table
        builder.ToTable("TermesGlossaireReglesQualite", "Gouvernance");

        // Configuration de la clé primaire
        builder.HasKey(tgrq => tgrq.Id);

        // Configuration des propriétés
        builder.Property(tgrq => tgrq.TermeGlossaireId)
            .IsRequired();

        builder.Property(tgrq => tgrq.RegleQualiteId)
            .IsRequired();

        builder.Property(tgrq => tgrq.TypeValidation)
            .IsRequired()
            .HasMaxLength(50)
            .HasDefaultValue("Conformite");

        builder.Property(tgrq => tgrq.UtilisateurAssociation)
            .HasMaxLength(100);

        builder.Property(tgrq => tgrq.DateAssociation)
            .IsRequired()
            .HasDefaultValueSql("GETUTCDATE()");

        builder.Property(tgrq => tgrq.EstObligatoire)
            .IsRequired()
            .HasDefaultValue(false);

        builder.Property(tgrq => tgrq.Priorite)
            .IsRequired()
            .HasDefaultValue(3);

        builder.Property(tgrq => tgrq.SeuilSpecifique)
            .HasPrecision(5, 2);

        builder.Property(tgrq => tgrq.Commentaires)
            .HasMaxLength(500);

        builder.Property(tgrq => tgrq.EstActive)
            .IsRequired()
            .HasDefaultValue(true);

        // Configuration des relations
        builder.HasOne(tgrq => tgrq.TermeGlossaire)
            .WithMany(tg => tg.AssociationsReglesQualite)
            .HasForeignKey(tgrq => tgrq.TermeGlossaireId)
            .OnDelete(DeleteBehavior.Cascade);

        builder.HasOne(tgrq => tgrq.RegleQualite)
            .WithMany(rq => rq.AssociationsTermesGlossaire)
            .HasForeignKey(tgrq => tgrq.RegleQualiteId)
            .OnDelete(DeleteBehavior.Cascade);

        // Configuration des index
        builder.HasIndex(tgrq => tgrq.TermeGlossaireId)
            .HasDatabaseName("IX_TermesGlossaireReglesQualite_TermeGlossaireId");

        builder.HasIndex(tgrq => tgrq.RegleQualiteId)
            .HasDatabaseName("IX_TermesGlossaireReglesQualite_RegleQualiteId");

        builder.HasIndex(tgrq => tgrq.TypeValidation)
            .HasDatabaseName("IX_TermesGlossaireReglesQualite_TypeValidation");

        builder.HasIndex(tgrq => tgrq.EstObligatoire)
            .HasDatabaseName("IX_TermesGlossaireReglesQualite_EstObligatoire");

        builder.HasIndex(tgrq => tgrq.EstActive)
            .HasDatabaseName("IX_TermesGlossaireReglesQualite_EstActive");

        // Index unique pour éviter les doublons
        builder.HasIndex(tgrq => new { tgrq.TermeGlossaireId, tgrq.RegleQualiteId })
            .IsUnique()
            .HasDatabaseName("UX_TermesGlossaireReglesQualite_TermeRegle");
    }
}
