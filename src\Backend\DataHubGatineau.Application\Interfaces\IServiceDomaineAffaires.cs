using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Application.Interfaces;

/// <summary>
/// Interface pour les services de gestion des domaines d'affaires.
/// </summary>
public interface IServiceDomaineAffaires
{
    /// <summary>
    /// Obtient tous les domaines d'affaires.
    /// </summary>
    /// <returns>Une collection de domaines d'affaires.</returns>
    Task<IEnumerable<DomaineAffaires>> ObtenirTousAsync();

    /// <summary>
    /// Obtient un domaine d'affaires par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <returns>Le domaine d'affaires si trouvé, sinon null.</returns>
    Task<DomaineAffaires?> ObtenirParIdAsync(Guid id);

    /// <summary>
    /// Ajoute un nouveau domaine d'affaires.
    /// </summary>
    /// <param name="domaineAffaires">Domaine d'affaires à ajouter.</param>
    /// <returns>Le domaine d'affaires ajouté.</returns>
    Task<DomaineAffaires> AjouterAsync(DomaineAffaires domaineAffaires);

    /// <summary>
    /// Met à jour un domaine d'affaires existant.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <param name="domaineAffaires">Domaine d'affaires à mettre à jour.</param>
    /// <returns>Le domaine d'affaires mis à jour.</returns>
    Task<DomaineAffaires> MettreAJourAsync(Guid id, DomaineAffaires domaineAffaires);

    /// <summary>
    /// Supprime un domaine d'affaires.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires à supprimer.</param>
    /// <returns>True si la suppression a réussi, false sinon.</returns>
    Task<bool> SupprimerAsync(Guid id);

    /// <summary>
    /// Obtient tous les domaines d'affaires actifs, triés par ordre d'affichage.
    /// </summary>
    /// <returns>Une collection de domaines d'affaires actifs.</returns>
    Task<IEnumerable<DomaineAffaires>> ObtenirActifsAsync();

    /// <summary>
    /// Obtient un domaine d'affaires par son code.
    /// </summary>
    /// <param name="code">Code du domaine d'affaires.</param>
    /// <returns>Le domaine d'affaires correspondant au code, ou null si non trouvé.</returns>
    Task<DomaineAffaires?> ObtenirParCodeAsync(string code);

    /// <summary>
    /// Vérifie si un nom de domaine d'affaires existe déjà.
    /// </summary>
    /// <param name="nom">Nom à vérifier.</param>
    /// <param name="idExclure">ID à exclure de la vérification (pour les modifications).</param>
    /// <returns>True si le nom existe déjà, false sinon.</returns>
    Task<bool> NomExisteAsync(string nom, Guid? idExclure = null);

    /// <summary>
    /// Vérifie si un code de domaine d'affaires existe déjà.
    /// </summary>
    /// <param name="code">Code à vérifier.</param>
    /// <param name="idExclure">ID à exclure de la vérification (pour les modifications).</param>
    /// <returns>True si le code existe déjà, false sinon.</returns>
    Task<bool> CodeExisteAsync(string code, Guid? idExclure = null);

    /// <summary>
    /// Active ou désactive un domaine d'affaires.
    /// </summary>
    /// <param name="id">ID du domaine d'affaires.</param>
    /// <param name="estActif">Nouvel état d'activation.</param>
    /// <returns>True si l'opération a réussi, false sinon.</returns>
    Task<bool> ChangerEtatActivationAsync(Guid id, bool estActif);

    /// <summary>
    /// Obtient les statistiques d'utilisation d'un domaine d'affaires.
    /// </summary>
    /// <param name="id">ID du domaine d'affaires.</param>
    /// <returns>Statistiques d'utilisation.</returns>
    Task<StatistiquesDomaineAffaires> ObtenirStatistiquesAsync(Guid id);
}

/// <summary>
/// Statistiques d'utilisation d'un domaine d'affaires.
/// </summary>
public class StatistiquesDomaineAffaires
{
    /// <summary>
    /// Nombre de termes du glossaire associés.
    /// </summary>
    public int NombreTermesGlossaire { get; set; }

    /// <summary>
    /// Date de création du premier terme associé.
    /// </summary>
    public DateTime? DatePremierTerme { get; set; }

    /// <summary>
    /// Date de création du dernier terme associé.
    /// </summary>
    public DateTime? DateDernierTerme { get; set; }
}
