using DataHubGatineau.Web.Models;
using DataHubGatineau.Web.Services.Interfaces;

namespace DataHubGatineau.Web.Services.Implementations;

/// <summary>
/// Service pour les opérations sur les termes du glossaire.
/// </summary>
public class TermeGlossaireService : ServiceBaseGuid<TermeGlossaire>, ITermeGlossaireService
{
    private static readonly List<TermeGlossaire> _mockData = new()
    {
        new TermeGlossaire
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000201"),
            Nom = "Client",
            Definition = "Personne physique ou morale qui achète des produits ou services de l'entreprise",
            Exemples = "Particulier, entreprise, organisme public",
            Synonymes = "Acheteur, consommateur",
            DomaineAffaires = "Marketing",
            Proprietaire = "Service à la clientèle",
            DateCreation = DateTime.Now.AddDays(-30),
            DateModification = DateTime.Now.AddDays(-5)
        },
        new TermeGlossaire
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000202"),
            Nom = "Facture",
            Definition = "Document commercial qui atteste d'une transaction entre un vendeur et un acheteur",
            Exemples = "Facture de vente, facture d'achat",
            Synonymes = "Note, relevé",
            DomaineAffaires = "Finance",
            Proprietaire = "Service financier",
            DateCreation = DateTime.Now.AddDays(-25),
            DateModification = DateTime.Now.AddDays(-3)
        },
        new TermeGlossaire
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000203"),
            Nom = "Employé",
            Definition = "Personne qui travaille pour l'entreprise en échange d'une rémunération",
            Exemples = "Employé à temps plein, employé à temps partiel, contractuel",
            Synonymes = "Salarié, travailleur",
            DomaineAffaires = "Ressources Humaines",
            Proprietaire = "Service RH",
            DateCreation = DateTime.Now.AddDays(-20),
            DateModification = DateTime.Now.AddDays(-2)
        },
        new TermeGlossaire
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000204"),
            Nom = "Produit",
            Definition = "Bien matériel ou immatériel proposé à la vente par l'entreprise",
            Exemples = "Produit physique, service, abonnement",
            Synonymes = "Article, marchandise",
            DomaineAffaires = "Marketing",
            Proprietaire = "Service produit",
            DateCreation = DateTime.Now.AddDays(-15),
            DateModification = DateTime.Now.AddDays(-1)
        },
        new TermeGlossaire
        {
            Id = Guid.Parse("00000000-0000-0000-0000-000000000205"),
            Nom = "Fournisseur",
            Definition = "Entreprise ou personne qui fournit des biens ou services à l'entreprise",
            Exemples = "Fournisseur de matières premières, prestataire de services",
            Synonymes = "Prestataire, vendeur",
            DomaineAffaires = "Achats",
            Proprietaire = "Service achats",
            DateCreation = DateTime.Now.AddDays(-10),
            DateModification = DateTime.Now
        }
    };

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="TermeGlossaireService"/>.
    /// </summary>
    /// <param name="httpClient">Client HTTP.</param>
    public TermeGlossaireService(HttpClient httpClient)
        : base(httpClient, "api/v1/TermesGlossaire")
    {
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<TermeGlossaire>> ObtenirTousAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync(_baseUrl);
            response.EnsureSuccessStatusCode();
            var termes = await response.Content.ReadFromJsonAsync<IEnumerable<TermeGlossaire>>();
            return termes ?? Enumerable.Empty<TermeGlossaire>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'obtention de tous les termes: {ex.Message}");
            // En cas d'erreur, retourne les données de test comme fallback
            return await Task.FromResult(_mockData);
        }
    }

    /// <inheritdoc/>
    public override async Task<TermeGlossaire?> ObtenirParIdAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/{id}");
            response.EnsureSuccessStatusCode();
            var terme = await response.Content.ReadFromJsonAsync<TermeGlossaire>();
            return terme;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'obtention du terme par ID: {ex.Message}");
            // En cas d'erreur, retourne l'élément correspondant à l'ID depuis les données de test
            return await Task.FromResult(_mockData.FirstOrDefault(e => e.Id.Equals(id)));
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirParDomaineAffairesAsync(string domaineAffaires)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/domaine/{domaineAffaires}");
            response.EnsureSuccessStatusCode();
            var termes = await response.Content.ReadFromJsonAsync<IEnumerable<TermeGlossaire>>();
            return termes ?? Enumerable.Empty<TermeGlossaire>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'obtention des termes par domaine d'affaires: {ex.Message}");
            // En cas d'erreur, filtre par domaine d'affaires depuis les données de test
            return await Task.FromResult(_mockData.Where(e => e.DomaineAffaires.Equals(domaineAffaires, StringComparison.OrdinalIgnoreCase)));
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirSousTermesAsync(Guid termeParentId)
    {
        // En mode mock, filtre par terme parent
        return await Task.FromResult(_mockData.Where(e => e.TermeParentId.Equals(termeParentId)));
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> RechercherParNomAsync(string terme)
    {
        // En mode mock, recherche par nom
        return await Task.FromResult(_mockData.Where(e => e.Nom.Contains(terme, StringComparison.OrdinalIgnoreCase) ||
                                                         e.Definition.Contains(terme, StringComparison.OrdinalIgnoreCase) ||
                                                         (e.Synonymes != null && e.Synonymes.Contains(terme, StringComparison.OrdinalIgnoreCase))));
    }

    /// <inheritdoc/>
    public override async Task<TermeGlossaire> AjouterAsync(TermeGlossaire entite)
    {
        // En mode mock, ajoute l'élément aux données de test
        entite.Id = Guid.NewGuid();
        entite.DateCreation = DateTime.Now;
        entite.DateModification = DateTime.Now;
        _mockData.Add(entite);
        return await Task.FromResult(entite);
    }

    /// <inheritdoc/>
    public override async Task<TermeGlossaire> MettreAJourAsync(Guid id, TermeGlossaire entite)
    {
        // En mode mock, met à jour l'élément dans les données de test
        var index = _mockData.FindIndex(e => e.Id.Equals(id));
        if (index >= 0)
        {
            entite.Id = id;
            entite.DateModification = DateTime.Now;
            _mockData[index] = entite;
            return await Task.FromResult(entite);
        }
        throw new KeyNotFoundException($"Terme du glossaire avec ID {id} non trouvé.");
    }

    /// <inheritdoc/>
    public override async Task<bool> SupprimerAsync(Guid id)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{id}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la suppression du terme du glossaire: {ex.Message}");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/actif/{actifDonneesId}");
            response.EnsureSuccessStatusCode();
            var termes = await response.Content.ReadFromJsonAsync<IEnumerable<TermeGlossaire>>();
            return termes ?? Enumerable.Empty<TermeGlossaire>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'obtention des termes par actif de données: {ex.Message}");
            return Enumerable.Empty<TermeGlossaire>();
        }
    }

    /// <inheritdoc/>
    public async Task<bool> AssocierActifDonneesAsync(Guid termeId, Guid actifDonneesId)
    {
        try
        {
            var response = await _httpClient.PostAsync($"{_baseUrl}/{termeId}/actifs/{actifDonneesId}", null);
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'association du terme à l'actif de données: {ex.Message}");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> DissocierActifDonneesAsync(Guid termeId, Guid actifDonneesId)
    {
        try
        {
            var response = await _httpClient.DeleteAsync($"{_baseUrl}/{termeId}/actifs/{actifDonneesId}");
            return response.IsSuccessStatusCode;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de la dissociation du terme de l'actif de données: {ex.Message}");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirTermesRacinesAsync()
    {
        try
        {
            var response = await _httpClient.GetAsync($"{_baseUrl}/racines");
            response.EnsureSuccessStatusCode();
            var termes = await response.Content.ReadFromJsonAsync<IEnumerable<TermeGlossaire>>();
            return termes ?? Enumerable.Empty<TermeGlossaire>();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Erreur lors de l'obtention des termes racines: {ex.Message}");
            // En cas d'erreur, retourne les termes sans parent depuis les données de test
            return await Task.FromResult(_mockData.Where(e => e.TermeParentId == null || e.TermeParentId == 0));
        }
    }
}

