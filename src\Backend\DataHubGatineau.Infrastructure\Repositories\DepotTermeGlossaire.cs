using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les termes du glossaire.
/// </summary>
public class DepotTermeGlossaire : DepotBase<TermeGlossaire>, IDepotTermeGlossaire
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotTermeGlossaire"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotTermeGlossaire(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <inheritdoc/>
    public override async Task<TermeGlossaire?> ObtenirParIdAsync(Guid id)
    {
        return await _dbSet
            .Include(t => t.TermeParent)
            .Include(t => t.TermesEnfants)
            // Comentamos esta línea para evitar errores con columnas que no existen en la BD
            //.Include(t => t.ActifsDonnees)
            .FirstOrDefaultAsync(t => t.Id == id);
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<TermeGlossaire>> ObtenirTousAsync()
    {
        return await _dbSet
            .Include(t => t.TermeParent)
            .Include(t => t.TermesEnfants)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirParDomaineAffairesAsync(string domaineAffaires)
    {
        return await _dbSet
            .Where(t => t.DomaineAffaires == domaineAffaires)
            .Include(t => t.TermeParent)
            .Include(t => t.TermesEnfants)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirParProprietaireAsync(string proprietaire)
    {
        return await _dbSet
            .Where(t => t.Proprietaire == proprietaire)
            .Include(t => t.TermeParent)
            .Include(t => t.TermesEnfants)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirTermesEnfantsAsync(Guid termeParentId)
    {
        return await _dbSet
            .Where(t => t.TermeParentId == termeParentId)
            .Include(t => t.TermesEnfants)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirTermesRacinesAsync()
    {
        return await _dbSet
            .Where(t => t.TermeParentId == null)
            .Include(t => t.TermesEnfants)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> RechercherAsync(string motCle)
    {
        return await _dbSet
            .Where(t => t.Nom.Contains(motCle) ||
                         t.Definition.Contains(motCle) ||
                         t.Synonymes.Contains(motCle) ||
                         t.Exemples.Contains(motCle))
            .Include(t => t.TermeParent)
            .Include(t => t.TermesEnfants)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<TermeGlossaire>> ObtenirParActifDonneesAsync(Guid actifDonneesId)
    {
        try
        {
            return await _dbSet
                .Where(t => t.ActifsDonnees.Any(a => a.Id == actifDonneesId))
                .Include(t => t.TermeParent)
                .Include(t => t.TermesEnfants)
                .ToListAsync();
        }
        catch (Exception ex)
        {
            // Log l'erreur pour debugging
            Console.WriteLine($"Erreur lors de la récupération des termes pour l'actif {actifDonneesId}: {ex.Message}");
            return Enumerable.Empty<TermeGlossaire>();
        }
    }

    /// <inheritdoc/>
    public async Task<bool> AssocierActifDonneesAsync(Guid termeId, Guid actifDonneesId)
    {
        try
        {
            var terme = await _dbSet
                .Include(t => t.ActifsDonnees)
                .FirstOrDefaultAsync(t => t.Id == termeId);

            if (terme == null)
                return false;

            var actifDonnees = await _context.ActifsDonnees.FindAsync(actifDonneesId);
            if (actifDonnees == null)
                return false;

            // Vérifier si l'association existe déjà
            if (terme.ActifsDonnees.Any(a => a.Id == actifDonneesId))
                return true; // L'association existe déjà

            terme.ActifsDonnees.Add(actifDonnees);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            // Log l'erreur pour debugging
            Console.WriteLine($"Erreur lors de l'association terme-actif: {ex.Message}");
            return false;
        }
    }

    /// <inheritdoc/>
    public async Task<bool> DissocierActifDonneesAsync(Guid termeId, Guid actifDonneesId)
    {
        try
        {
            var terme = await _dbSet
                .Include(t => t.ActifsDonnees)
                .FirstOrDefaultAsync(t => t.Id == termeId);

            if (terme == null)
                return false;

            var actifDonnees = terme.ActifsDonnees.FirstOrDefault(a => a.Id == actifDonneesId);
            if (actifDonnees == null)
                return false; // L'association n'existe pas

            terme.ActifsDonnees.Remove(actifDonnees);
            await _context.SaveChangesAsync();
            return true;
        }
        catch (Exception ex)
        {
            // Log l'erreur pour debugging
            Console.WriteLine($"Erreur lors de la dissociation terme-actif: {ex.Message}");
            return false;
        }
    }
}
