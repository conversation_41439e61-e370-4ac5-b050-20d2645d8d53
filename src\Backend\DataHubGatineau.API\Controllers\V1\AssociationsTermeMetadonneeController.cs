using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Domain.Interfaces.Repositories;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers.V1;

/// <summary>
/// Contrôleur pour la gestion des associations entre termes du glossaire et métadonnées.
/// </summary>
[ApiController]
[Route("api/v1/[controller]")]
[Produces("application/json")]
public class AssociationsTermeMetadonneeController : ControllerBase
{
    private readonly IDepotTermeGlossaireMetadonnee _depotAssociation;
    private readonly IDepotTermeGlossaire _depotTermeGlossaire;
    private readonly IDepotMetadonnee _depotMetadonnee;
    private readonly ILogger<AssociationsTermeMetadonneeController> _logger;

    /// <summary>
    /// Initialise une nouvelle instance du contrôleur des associations terme-métadonnée.
    /// </summary>
    public AssociationsTermeMetadonneeController(
        IDepotTermeGlossaireMetadonnee depotAssociation,
        IDepotTermeGlossaire depotTermeGlossaire,
        IDepotMetadonnee depotMetadonnee,
        ILogger<AssociationsTermeMetadonneeController> logger)
    {
        _depotAssociation = depotAssociation;
        _depotTermeGlossaire = depotTermeGlossaire;
        _depotMetadonnee = depotMetadonnee;
        _logger = logger;
    }

    /// <summary>
    /// Obtient toutes les associations avec leurs entités liées.
    /// </summary>
    /// <returns>Liste des associations.</returns>
    [HttpGet]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireMetadonnee>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireMetadonnee>>> ObtenirToutesLesAssociations()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirTousAvecEntitesLieesAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations terme-métadonnée");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient une association par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association trouvée.</returns>
    [HttpGet("{id:guid}")]
    [ProducesResponseType(typeof(TermeGlossaireMetadonnee), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TermeGlossaireMetadonnee>> ObtenirAssociationParId(Guid id)
    {
        try
        {
            var association = await _depotAssociation.ObtenirParIdAvecEntitesLieesAsync(id);
            if (association == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            return Ok(association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Liste des associations pour le terme.</returns>
    [HttpGet("terme/{termeGlossaireId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireMetadonnee>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireMetadonnee>>> ObtenirAssociationsParTerme(Guid termeGlossaireId)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParTermeGlossaireAsync(termeGlossaireId);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations pour le terme {TermeId}", termeGlossaireId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations pour une métadonnée.
    /// </summary>
    /// <param name="metadonneeId">Identifiant de la métadonnée.</param>
    /// <returns>Liste des associations pour la métadonnée.</returns>
    [HttpGet("metadonnee/{metadonneeId:guid}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireMetadonnee>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireMetadonnee>>> ObtenirAssociationsParMetadonnee(Guid metadonneeId)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParMetadonneeAsync(metadonneeId);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations pour la métadonnée {MetadonneeId}", metadonneeId);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations par type.
    /// </summary>
    /// <param name="typeAssociation">Type d'association.</param>
    /// <returns>Liste des associations du type spécifié.</returns>
    [HttpGet("type/{typeAssociation}")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireMetadonnee>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireMetadonnee>>> ObtenirAssociationsParType(string typeAssociation)
    {
        try
        {
            var associations = await _depotAssociation.ObtenirParTypeAssociationAsync(typeAssociation);
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations par type {Type}", typeAssociation);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les associations validées.
    /// </summary>
    /// <returns>Liste des associations validées.</returns>
    [HttpGet("validees")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireMetadonnee>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireMetadonnee>>> ObtenirAssociationsValidees()
    {
        try
        {
            var associations = await _depotAssociation.ObtenirAssociationsValideesAsync();
            return Ok(associations);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des associations validées");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les suggestions d'associations.
    /// </summary>
    /// <param name="scoreMinimum">Score de confiance minimum.</param>
    /// <returns>Liste des suggestions d'associations.</returns>
    [HttpGet("suggestions")]
    [ProducesResponseType(typeof(IEnumerable<TermeGlossaireMetadonnee>), StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<TermeGlossaireMetadonnee>>> ObtenirSuggestions([FromQuery] decimal scoreMinimum = 0.7m)
    {
        try
        {
            var suggestions = await _depotAssociation.ObtenirSuggestionsAsync(scoreMinimum);
            return Ok(suggestions);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des suggestions d'associations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Obtient les statistiques des associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    [HttpGet("statistiques")]
    [ProducesResponseType(typeof(Dictionary<string, int>), StatusCodes.Status200OK)]
    public async Task<ActionResult<Dictionary<string, int>>> ObtenirStatistiquesAssociations()
    {
        try
        {
            var statistiques = await _depotAssociation.ObtenirStatistiquesAsync();
            return Ok(statistiques);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la récupération des statistiques des associations");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Crée une nouvelle association entre un terme et une métadonnée.
    /// </summary>
    /// <param name="association">Association à créer.</param>
    /// <returns>Association créée.</returns>
    [HttpPost]
    [ProducesResponseType(typeof(TermeGlossaireMetadonnee), StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<TermeGlossaireMetadonnee>> CreerAssociation([FromBody] TermeGlossaireMetadonnee association)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            // Vérifier si l'association existe déjà
            if (await _depotAssociation.AssociationExisteAsync(association.TermeGlossaireId, association.MetadonneeId))
            {
                return BadRequest("Une association entre ce terme et cette métadonnée existe déjà");
            }

            // Vérifier que le terme et la métadonnée existent
            var terme = await _depotTermeGlossaire.ObtenirParIdAsync(association.TermeGlossaireId);
            if (terme == null)
            {
                return BadRequest($"Terme du glossaire avec l'ID {association.TermeGlossaireId} non trouvé");
            }

            var metadonnee = await _depotMetadonnee.ObtenirParIdAsync(association.MetadonneeId);
            if (metadonnee == null)
            {
                return BadRequest($"Métadonnée avec l'ID {association.MetadonneeId} non trouvée");
            }

            association.Id = Guid.NewGuid();
            association.DateAssociation = DateTime.UtcNow;
            association.DateCreation = DateTime.UtcNow;
            association.DateModification = DateTime.UtcNow;

            await _depotAssociation.AjouterAsync(association);
            
            return CreatedAtAction(nameof(ObtenirAssociationParId), new { id = association.Id }, association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la création de l'association");
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Met à jour une association existante.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="association">Données de l'association à mettre à jour.</param>
    /// <returns>Association mise à jour.</returns>
    [HttpPut("{id:guid}")]
    [ProducesResponseType(typeof(TermeGlossaireMetadonnee), StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<TermeGlossaireMetadonnee>> MettreAJourAssociation(Guid id, [FromBody] TermeGlossaireMetadonnee association)
    {
        try
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            if (id != association.Id)
            {
                return BadRequest("L'ID dans l'URL ne correspond pas à l'ID de l'association");
            }

            var associationExistante = await _depotAssociation.ObtenirParIdAsync(id);
            if (associationExistante == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            association.DateModification = DateTime.UtcNow;
            await _depotAssociation.MettreAJourAsync(association);

            return Ok(association);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la mise à jour de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }

    /// <summary>
    /// Supprime une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Résultat de l'opération.</returns>
    [HttpDelete("{id:guid}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult> SupprimerAssociation(Guid id)
    {
        try
        {
            var association = await _depotAssociation.ObtenirParIdAsync(id);
            if (association == null)
            {
                return NotFound($"Association avec l'ID {id} non trouvée");
            }

            await _depotAssociation.SupprimerAsync(association.Id);
            return NoContent();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Erreur lors de la suppression de l'association {Id}", id);
            return StatusCode(500, "Erreur interne du serveur");
        }
    }
}
