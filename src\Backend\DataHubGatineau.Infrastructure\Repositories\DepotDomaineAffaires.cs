using DataHubGatineau.Domain.Interfaces;
using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les domaines d'affaires.
/// </summary>
public class DepotDomaineAffaires : DepotBase<DomaineAffaires>, IDepotDomaineAffaires
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotDomaineAffaires"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotDomaineAffaires(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <inheritdoc/>
    public async Task<IEnumerable<DomaineAffaires>> ObtenirActifsAsync()
    {
        return await _dbSet
            .Where(d => d.EstActif)
            .OrderBy(d => d.Ordre)
            .ThenBy(d => d.Nom)
            .ToListAsync();
    }

    /// <inheritdoc/>
    public async Task<DomaineAffaires?> ObtenirParCodeAsync(string code)
    {
        return await _dbSet
            .FirstOrDefaultAsync(d => d.Code == code);
    }

    /// <inheritdoc/>
    public async Task<bool> NomExisteAsync(string nom, Guid? idExclure = null)
    {
        var query = _dbSet.Where(d => d.Nom.ToLower() == nom.ToLower());
        
        if (idExclure.HasValue)
        {
            query = query.Where(d => d.Id != idExclure.Value);
        }

        return await query.AnyAsync();
    }

    /// <inheritdoc/>
    public async Task<bool> CodeExisteAsync(string code, Guid? idExclure = null)
    {
        if (string.IsNullOrEmpty(code))
            return false;

        var query = _dbSet.Where(d => d.Code != null && d.Code.ToLower() == code.ToLower());
        
        if (idExclure.HasValue)
        {
            query = query.Where(d => d.Id != idExclure.Value);
        }

        return await query.AnyAsync();
    }

    /// <inheritdoc/>
    public async Task<bool> ChangerEtatActivationAsync(Guid id, bool estActif)
    {
        var domaine = await ObtenirParIdAsync(id);
        if (domaine == null)
            return false;

        domaine.EstActif = estActif;
        domaine.DateModification = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();
        return true;
    }

    /// <inheritdoc/>
    public async Task<(int NombreTermes, DateTime? DatePremier, DateTime? DateDernier)> ObtenirStatistiquesUtilisationAsync(Guid id)
    {
        var statistiques = await _context.TermesGlossaire
            .Where(t => t.DomaineAffairesId == id)
            .GroupBy(t => t.DomaineAffairesId)
            .Select(g => new
            {
                Nombre = g.Count(),
                DatePremier = g.Min(t => t.DateCreation),
                DateDernier = g.Max(t => t.DateCreation)
            })
            .FirstOrDefaultAsync();

        return statistiques != null 
            ? (statistiques.Nombre, statistiques.DatePremier, statistiques.DateDernier)
            : (0, null, null);
    }

    /// <inheritdoc/>
    public override async Task<IEnumerable<DomaineAffaires>> ObtenirTousAsync()
    {
        return await _dbSet
            .OrderBy(d => d.Ordre)
            .ThenBy(d => d.Nom)
            .ToListAsync();
    }
}
