// <auto-generated />
using System;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

#nullable disable

namespace DataHubGatineau.Infrastructure.Migrations
{
    [DbContext(typeof(CentreDonneesDbContext))]
    [Migration("20250602200000_MigrarDomainesAffaires")]
    partial class MigrarDomainesAffaires
    {
        /// <inheritdoc />
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("ProductVersion", "9.0.4")
                .HasAnnotation("Relational:MaxIdentifierLength", 128);

            SqlServerModelBuilderExtensions.UseIdentityColumns(modelBuilder);
            // Note: Le modèle complet sera généré automatiquement par Entity Framework
#pragma warning restore 612, 618
        }
    }
}
