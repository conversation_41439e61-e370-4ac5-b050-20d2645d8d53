-- Script para implementar integraciones faltantes del glosario de términos
-- Fecha: $(date)
-- Descripción: Agrega relaciones entre TermeGlossaire y otras entidades según estándares de la industria

USE [Gouvernance];
GO

PRINT '🔗 IMPLEMENTACIÓN DE INTEGRACIONES DEL GLOSARIO DE TÉRMINOS';
PRINT '=========================================================';

-- =====================================================
-- 1. INTEGRACIÓN: TermeGlossaire ↔ Metadonnee (Many-to-Many)
-- =====================================================

PRINT '';
PRINT '📋 1. CREANDO RELACIÓN: TermeGlossaire ↔ Metadonnee';
PRINT '---------------------------------------------------';

-- Crear tabla de relación Many-to-Many entre TermeGlossaire y Metadonnee
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'Gouvernance' 
    AND TABLE_NAME = 'TermesGlossaireMetadonnees')
BEGIN
    CREATE TABLE [Gouvernance].[TermesGlossaireMetadonnees] (
        [TermeGlossaireId] uniqueidentifier NOT NULL,
        [MetadonneeId] uniqueidentifier NOT NULL,
        [DateAssociation] datetime2 NOT NULL DEFAULT GETDATE(),
        [UtilisateurAssociation] nvarchar(100) NULL,
        [TypeAssociation] nvarchar(50) NOT NULL DEFAULT 'Manuel', -- Manuel, Automatique, Suggéré
        [ConfianceScore] decimal(3,2) NULL, -- Score de confiance pour associations automatiques
        
        CONSTRAINT [PK_TermesGlossaireMetadonnees] PRIMARY KEY ([TermeGlossaireId], [MetadonneeId]),
        
        CONSTRAINT [FK_TermesGlossaireMetadonnees_TermeGlossaire] 
            FOREIGN KEY ([TermeGlossaireId]) 
            REFERENCES [Gouvernance].[TermesGlossaire] ([Id]) 
            ON DELETE CASCADE,
            
        CONSTRAINT [FK_TermesGlossaireMetadonnees_Metadonnee] 
            FOREIGN KEY ([MetadonneeId]) 
            REFERENCES [Metadonnees].[Metadonnees] ([Id]) 
            ON DELETE CASCADE
    );
    
    PRINT '✅ Tabla TermesGlossaireMetadonnees creada exitosamente';
END
ELSE
BEGIN
    PRINT '✅ Tabla TermesGlossaireMetadonnees ya existe';
END

-- =====================================================
-- 2. INTEGRACIÓN: TermeGlossaire ↔ RegleQualite (Many-to-Many)
-- =====================================================

PRINT '';
PRINT '🔍 2. CREANDO RELACIÓN: TermeGlossaire ↔ RegleQualite';
PRINT '----------------------------------------------------';

-- Crear tabla de relación Many-to-Many entre TermeGlossaire y RegleQualite
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'Gouvernance' 
    AND TABLE_NAME = 'TermesGlossaireReglesQualite')
BEGIN
    CREATE TABLE [Gouvernance].[TermesGlossaireReglesQualite] (
        [TermeGlossaireId] uniqueidentifier NOT NULL,
        [RegleQualiteId] uniqueidentifier NOT NULL,
        [DateAssociation] datetime2 NOT NULL DEFAULT GETDATE(),
        [UtilisateurAssociation] nvarchar(100) NULL,
        [TypeValidation] nvarchar(50) NOT NULL DEFAULT 'Conformité', -- Conformité, Complétude, Exactitude
        [EstObligatoire] bit NOT NULL DEFAULT 0,
        
        CONSTRAINT [PK_TermesGlossaireReglesQualite] PRIMARY KEY ([TermeGlossaireId], [RegleQualiteId]),
        
        CONSTRAINT [FK_TermesGlossaireReglesQualite_TermeGlossaire] 
            FOREIGN KEY ([TermeGlossaireId]) 
            REFERENCES [Gouvernance].[TermesGlossaire] ([Id]) 
            ON DELETE CASCADE,
            
        CONSTRAINT [FK_TermesGlossaireReglesQualite_RegleQualite] 
            FOREIGN KEY ([RegleQualiteId]) 
            REFERENCES [Qualite].[ReglesQualite] ([Id]) 
            ON DELETE CASCADE
    );
    
    PRINT '✅ Tabla TermesGlossaireReglesQualite creada exitosamente';
END
ELSE
BEGIN
    PRINT '✅ Tabla TermesGlossaireReglesQualite ya existe';
END

-- =====================================================
-- 3. INTEGRACIÓN: TermeGlossaire ↔ Politique (Many-to-Many)
-- =====================================================

PRINT '';
PRINT '📜 3. CREANDO RELACIÓN: TermeGlossaire ↔ Politique';
PRINT '-------------------------------------------------';

-- Crear tabla de relación Many-to-Many entre TermeGlossaire y Politique
IF NOT EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES 
    WHERE TABLE_SCHEMA = 'Gouvernance' 
    AND TABLE_NAME = 'TermesGlossairePolitiques')
BEGIN
    CREATE TABLE [Gouvernance].[TermesGlossairePolitiques] (
        [TermeGlossaireId] uniqueidentifier NOT NULL,
        [PolitiqueId] uniqueidentifier NOT NULL,
        [DateAssociation] datetime2 NOT NULL DEFAULT GETDATE(),
        [UtilisateurAssociation] nvarchar(100) NULL,
        [TypeApplication] nvarchar(50) NOT NULL DEFAULT 'Classification', -- Classification, Accès, Rétention
        [NiveauImpact] nvarchar(20) NOT NULL DEFAULT 'Moyen', -- Faible, Moyen, Élevé, Critique
        
        CONSTRAINT [PK_TermesGlossairePolitiques] PRIMARY KEY ([TermeGlossaireId], [PolitiqueId]),
        
        CONSTRAINT [FK_TermesGlossairePolitiques_TermeGlossaire] 
            FOREIGN KEY ([TermeGlossaireId]) 
            REFERENCES [Gouvernance].[TermesGlossaire] ([Id]) 
            ON DELETE CASCADE,
            
        CONSTRAINT [FK_TermesGlossairePolitiques_Politique] 
            FOREIGN KEY ([PolitiqueId]) 
            REFERENCES [Gouvernance].[Politiques] ([Id]) 
            ON DELETE CASCADE
    );
    
    PRINT '✅ Tabla TermesGlossairePolitiques creada exitosamente';
END
ELSE
BEGIN
    PRINT '✅ Tabla TermesGlossairePolitiques ya existe';
END

-- =====================================================
-- 4. EXTENSIÓN: Agregar campos de integración a TermeGlossaire
-- =====================================================

PRINT '';
PRINT '🔧 4. EXTENDIENDO ENTIDAD TermeGlossaire';
PRINT '----------------------------------------';

-- Agregar campos para mejorar la integración
IF NOT EXISTS (SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossaire') 
    AND name = 'EstCertifie')
BEGIN
    ALTER TABLE [Gouvernance].[TermesGlossaire] 
    ADD [EstCertifie] bit NOT NULL DEFAULT 0;
    
    PRINT '✅ Campo EstCertifie agregado';
END

IF NOT EXISTS (SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossaire') 
    AND name = 'NiveauConfidentialite')
BEGIN
    ALTER TABLE [Gouvernance].[TermesGlossaire] 
    ADD [NiveauConfidentialite] nvarchar(20) NULL; -- Public, Interne, Confidentiel, Secret
    
    PRINT '✅ Campo NiveauConfidentialite agregado';
END

IF NOT EXISTS (SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossaire') 
    AND name = 'TagsMetier')
BEGIN
    ALTER TABLE [Gouvernance].[TermesGlossaire] 
    ADD [TagsMetier] nvarchar(500) NULL; -- Tags separados por comas
    
    PRINT '✅ Campo TagsMetier agregado';
END

IF NOT EXISTS (SELECT * FROM sys.columns 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossaire') 
    AND name = 'UtilisationCount')
BEGIN
    ALTER TABLE [Gouvernance].[TermesGlossaire] 
    ADD [UtilisationCount] int NOT NULL DEFAULT 0; -- Contador de uso
    
    PRINT '✅ Campo UtilisationCount agregado';
END

-- =====================================================
-- 5. CREAR ÍNDICES PARA OPTIMIZAR CONSULTAS
-- =====================================================

PRINT '';
PRINT '📈 5. CREANDO ÍNDICES DE OPTIMIZACIÓN';
PRINT '-------------------------------------';

-- Índices para TermesGlossaireMetadonnees
IF NOT EXISTS (SELECT * FROM sys.indexes 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossaireMetadonnees') 
    AND name = 'IX_TermesGlossaireMetadonnees_MetadonneeId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_TermesGlossaireMetadonnees_MetadonneeId] 
    ON [Gouvernance].[TermesGlossaireMetadonnees] ([MetadonneeId]);
    
    PRINT '✅ Índice IX_TermesGlossaireMetadonnees_MetadonneeId creado';
END

-- Índices para TermesGlossaireReglesQualite
IF NOT EXISTS (SELECT * FROM sys.indexes 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossaireReglesQualite') 
    AND name = 'IX_TermesGlossaireReglesQualite_RegleQualiteId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_TermesGlossaireReglesQualite_RegleQualiteId] 
    ON [Gouvernance].[TermesGlossaireReglesQualite] ([RegleQualiteId]);
    
    PRINT '✅ Índice IX_TermesGlossaireReglesQualite_RegleQualiteId creado';
END

-- Índices para TermesGlossairePolitiques
IF NOT EXISTS (SELECT * FROM sys.indexes 
    WHERE object_id = OBJECT_ID('Gouvernance.TermesGlossairePolitiques') 
    AND name = 'IX_TermesGlossairePolitiques_PolitiqueId')
BEGIN
    CREATE NONCLUSTERED INDEX [IX_TermesGlossairePolitiques_PolitiqueId] 
    ON [Gouvernance].[TermesGlossairePolitiques] ([PolitiqueId]);
    
    PRINT '✅ Índice IX_TermesGlossairePolitiques_PolitiqueId creado';
END

-- =====================================================
-- 6. INSERTAR DATOS DE EJEMPLO
-- =====================================================

PRINT '';
PRINT '📝 6. INSERTANDO DATOS DE EJEMPLO';
PRINT '---------------------------------';

-- Ejemplo de asociación entre término y metadato
IF EXISTS (SELECT TOP 1 * FROM [Gouvernance].[TermesGlossaire]) 
   AND EXISTS (SELECT TOP 1 * FROM [Metadonnees].[Metadonnees])
BEGIN
    DECLARE @TermeId uniqueidentifier = (SELECT TOP 1 Id FROM [Gouvernance].[TermesGlossaire]);
    DECLARE @MetadonneeId uniqueidentifier = (SELECT TOP 1 Id FROM [Metadonnees].[Metadonnees]);
    
    IF NOT EXISTS (SELECT * FROM [Gouvernance].[TermesGlossaireMetadonnees] 
                   WHERE TermeGlossaireId = @TermeId AND MetadonneeId = @MetadonneeId)
    BEGIN
        INSERT INTO [Gouvernance].[TermesGlossaireMetadonnees] 
        (TermeGlossaireId, MetadonneeId, TypeAssociation, ConfianceScore)
        VALUES (@TermeId, @MetadonneeId, 'Exemple', 0.95);
        
        PRINT '✅ Ejemplo de asociación TermeGlossaire-Metadonnee creado';
    END
END

PRINT '';
PRINT '🎉 IMPLEMENTACIÓN DE INTEGRACIONES COMPLETADA';
PRINT '=============================================';
PRINT 'Las siguientes integraciones han sido implementadas:';
PRINT '✅ TermeGlossaire ↔ Metadonnee (Many-to-Many)';
PRINT '✅ TermeGlossaire ↔ RegleQualite (Many-to-Many)';
PRINT '✅ TermeGlossaire ↔ Politique (Many-to-Many)';
PRINT '✅ Campos extendidos en TermeGlossaire';
PRINT '✅ Índices de optimización';
PRINT '';
PRINT '📋 PRÓXIMOS PASOS:';
PRINT '1. Actualizar modelos de dominio en C#';
PRINT '2. Actualizar servicios y repositorios';
PRINT '3. Implementar UI para gestionar asociaciones';
PRINT '4. Crear funcionalidades de propagación automática';
