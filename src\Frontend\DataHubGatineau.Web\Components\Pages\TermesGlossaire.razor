@page "/glossaire"
@page "/termes-glossaire"
@using DataHubGatineau.Web.Models
@using DataHubGatineau.Web.Services.Interfaces
@inject ITermeGlossaireService TermeGlossaireService
@inject IDomaineGouvernanceService DomaineGouvernanceService
@inject NavigationManager NavigationManager
@inject IJSRuntime JSRuntime

<PageTitle>Glossaire de Termes - DataHub Gatineau</PageTitle>

<div class="glossaire-page">
    <!-- Header avec recherche -->
    <div class="page-header bg-light py-4 mb-4">
        <div class="container-fluid">
            <div class="row align-items-center">
                <div class="col-md-6">
                    <h1 class="h3 mb-0">
                        <i class="bi bi-book text-primary me-2"></i>
                        Glossaire de Termes
                    </h1>
                    <p class="text-muted mb-0">Dictionnaire des termes métier et techniques</p>
                </div>
                <div class="col-md-6 text-end">
                    <button class="btn btn-primary" @onclick="AjouterTerme">
                        <i class="bi bi-plus-circle me-1"></i>
                        Ajouter un terme
                    </button>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid">
        <!-- Barre de recherche et filtres -->
        <div class="row mb-4">
            <div class="col-lg-8">
                <div class="search-container">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="bi bi-search"></i>
                        </span>
                        <input type="text" class="form-control" 
                               placeholder="Rechercher un terme, définition, synonyme..."
                               @bind="_searchTerm" 
                               @onkeypress="OnSearchKeyPress" />
                        <button class="btn btn-outline-secondary" @onclick="RechercherTermes">
                            Rechercher
                        </button>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="filter-container">
                    <select class="form-select" @bind="SelectedDomaineId">
                        <option value="">Tous les domaines</option>
                        @foreach (var domaine in _domaines)
                        {
                            <option value="@domaine.Id">@domaine.Nom</option>
                        }
                    </select>
                </div>
            </div>
        </div>

        <!-- Statistiques -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-book"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@_totalTermes</h3>
                        <p>Termes totaux</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-folder"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@_domaines.Count</h3>
                        <p>Domaines</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-diagram-3"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@_termesRacines.Count</h3>
                        <p>Termes racines</p>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="stat-card">
                    <div class="stat-icon">
                        <i class="bi bi-search"></i>
                    </div>
                    <div class="stat-content">
                        <h3>@_termesAffiches.Count</h3>
                        <p>Résultats</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation alphabétique -->
        <div class="alphabet-nav mb-4">
            <div class="d-flex flex-wrap gap-2">
                <button class="btn btn-sm @(_selectedLetter == "" ? "btn-primary" : "btn-outline-primary")" 
                        @onclick='() => FiltrerParLettre("")'>
                    Tous
                </button>
                @foreach (var letter in "ABCDEFGHIJKLMNOPQRSTUVWXYZ")
                {
                    <button class="btn btn-sm @(_selectedLetter == letter.ToString() ? "btn-primary" : "btn-outline-primary")" 
                            @onclick='() => FiltrerParLettre(letter.ToString())'>
                        @letter
                    </button>
                }
            </div>
        </div>

        <!-- Liste des termes -->
        @if (_loading)
        {
            <div class="text-center py-5">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Chargement...</span>
                </div>
                <p class="mt-2">Chargement des termes...</p>
            </div>
        }
        else if (!_termesAffiches.Any())
        {
            <div class="empty-state text-center py-5">
                <i class="bi bi-book display-1 text-muted"></i>
                <h3 class="mt-3">Aucun terme trouvé</h3>
                <p class="text-muted">
                    @if (!string.IsNullOrEmpty(_searchTerm))
                    {
                        <span>Aucun terme ne correspond à votre recherche "<strong>@_searchTerm</strong>".</span>
                    }
                    else
                    {
                        <span>Le glossaire ne contient aucun terme pour les critères sélectionnés.</span>
                    }
                </p>
                <button class="btn btn-primary mt-3" @onclick="AjouterTerme">
                    <i class="bi bi-plus-circle me-1"></i>
                    Ajouter le premier terme
                </button>
            </div>
        }
        else
        {
            <div class="termes-grid">
                @foreach (var terme in _termesAffiches)
                {
                    <div class="terme-card" @onclick="() => VoirDetails(terme.Id)">
                        <div class="terme-header">
                            <h5 class="terme-nom">@terme.Nom</h5>
                            @if (!string.IsNullOrEmpty(terme.DomaineAffaires))
                            {
                                <span class="badge bg-primary">@terme.DomaineAffaires</span>
                            }
                        </div>
                        <div class="terme-definition">
                            @if (terme.Definition.Length > 150)
                            {
                                <span>@(terme.Definition.Substring(0, 147))...</span>
                            }
                            else
                            {
                                <span>@terme.Definition</span>
                            }
                        </div>
                        @if (!string.IsNullOrEmpty(terme.Synonymes))
                        {
                            <div class="terme-synonymes">
                                <small class="text-muted">
                                    <i class="bi bi-arrow-repeat me-1"></i>
                                    Synonymes: @terme.Synonymes
                                </small>
                            </div>
                        }
                        <div class="terme-footer">
                            <small class="text-muted">
                                <i class="bi bi-person me-1"></i>
                                @terme.Proprietaire
                            </small>
                            <div class="terme-actions">
                                <button class="btn btn-sm btn-outline-primary" @onclick:stopPropagation="true" @onclick="() => ModifierTerme(terme.Id)">
                                    <i class="bi bi-pencil"></i>
                                </button>
                                <button class="btn btn-sm btn-outline-danger" @onclick:stopPropagation="true" @onclick="() => SupprimerTerme(terme.Id)">
                                    <i class="bi bi-trash"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                }
            </div>
        }
    </div>
</div>

<style>
    .glossaire-page {
        min-height: 100vh;
    }

    .search-container .input-group {
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-radius: 8px;
        overflow: hidden;
    }

    .stat-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        display: flex;
        align-items: center;
        gap: 1rem;
    }

    .stat-icon {
        width: 50px;
        height: 50px;
        background: linear-gradient(135deg, #007bff, #0056b3);
        border-radius: 12px;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-size: 1.5rem;
    }

    .stat-content h3 {
        margin: 0;
        font-size: 2rem;
        font-weight: bold;
        color: #333;
    }

    .stat-content p {
        margin: 0;
        color: #666;
        font-size: 0.9rem;
    }

    .alphabet-nav {
        background: white;
        padding: 1rem;
        border-radius: 12px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }

    .termes-grid {
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
        gap: 1.5rem;
    }

    .terme-card {
        background: white;
        border-radius: 12px;
        padding: 1.5rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
        cursor: pointer;
        border: 1px solid #e9ecef;
    }

    .terme-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 16px rgba(0,0,0,0.15);
        border-color: #007bff;
    }

    .terme-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;
    }

    .terme-nom {
        margin: 0;
        color: #007bff;
        font-weight: 600;
    }

    .terme-definition {
        color: #333;
        line-height: 1.5;
        margin-bottom: 1rem;
    }

    .terme-synonymes {
        margin-bottom: 1rem;
    }

    .terme-footer {
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-top: 1px solid #e9ecef;
        padding-top: 1rem;
    }

    .terme-actions {
        display: flex;
        gap: 0.5rem;
    }

    .empty-state {
        background: white;
        border-radius: 12px;
        padding: 3rem;
        box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    }
</style>

@code {
    private List<TermeGlossaire> _termes = new();
    private List<TermeGlossaire> _termesAffiches = new();
    private List<TermeGlossaire> _termesRacines = new();
    private List<DomaineGouvernance> _domaines = new();
    private bool _loading = true;
    private string _searchTerm = string.Empty;
    private string _selectedDomaineId = string.Empty;
    private string _selectedLetter = string.Empty;
    private int _totalTermes = 0;

    protected override async Task OnInitializedAsync()
    {
        await ChargerDonnees();
    }

    private async Task ChargerDonnees()
    {
        _loading = true;
        try
        {
            // Charger tous les termes
            var termes = await TermeGlossaireService.ObtenirTousAsync();
            _termes = termes.ToList();
            _termesAffiches = _termes;
            _totalTermes = _termes.Count;

            // Charger les termes racines
            var termesRacines = await TermeGlossaireService.ObtenirTermesRacinesAsync();
            _termesRacines = termesRacines.ToList();

            // Charger les domaines
            var domaines = await DomaineGouvernanceService.ObtenirTousAsync();
            _domaines = domaines.ToList();
        }
        catch (Exception ex)
        {
            Console.Error.WriteLine($"Erreur lors du chargement des données: {ex.Message}");
        }
        finally
        {
            _loading = false;
        }
    }

    private async Task RechercherTermes()
    {
        if (string.IsNullOrWhiteSpace(_searchTerm))
        {
            _termesAffiches = _termes;
        }
        else
        {
            try
            {
                var resultats = await TermeGlossaireService.RechercherParNomAsync(_searchTerm);
                _termesAffiches = resultats.ToList();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Erreur lors de la recherche: {ex.Message}");
                // Fallback: recherche locale
                _termesAffiches = _termes.Where(t =>
                    t.Nom.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    t.Definition.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase) ||
                    (!string.IsNullOrEmpty(t.Synonymes) && t.Synonymes.Contains(_searchTerm, StringComparison.OrdinalIgnoreCase))
                ).ToList();
            }
        }

        AppliquerFiltres();
    }

    private async Task OnSearchKeyPress(KeyboardEventArgs e)
    {
        if (e.Key == "Enter")
        {
            await RechercherTermes();
        }
    }

    private string SelectedDomaineId
    {
        get => _selectedDomaineId;
        set
        {
            _selectedDomaineId = value;
            AppliquerFiltres();
        }
    }

    private void FiltrerParLettre(string letter)
    {
        _selectedLetter = letter;
        AppliquerFiltres();
    }

    private void AppliquerFiltres()
    {
        var termesFiltrés = _termesAffiches.AsEnumerable();

        // Filtre par domaine
        if (!string.IsNullOrEmpty(_selectedDomaineId) && Guid.TryParse(_selectedDomaineId, out var domaineId))
        {
            termesFiltrés = termesFiltrés.Where(t => t.DomaineGouvernanceId == domaineId);
        }

        // Filtre par lettre
        if (!string.IsNullOrEmpty(_selectedLetter))
        {
            termesFiltrés = termesFiltrés.Where(t => t.Nom.StartsWith(_selectedLetter, StringComparison.OrdinalIgnoreCase));
        }

        _termesAffiches = termesFiltrés.ToList();
        StateHasChanged();
    }

    private void VoirDetails(Guid termeId)
    {
        NavigationManager.NavigateTo($"/termes-glossaire/{termeId}");
    }

    private void AjouterTerme()
    {
        NavigationManager.NavigateTo("/termes-glossaire/ajouter");
    }

    private void ModifierTerme(Guid termeId)
    {
        NavigationManager.NavigateTo($"/termes-glossaire/modifier/{termeId}");
    }

    private async Task SupprimerTerme(Guid termeId)
    {
        var terme = _termes.FirstOrDefault(t => t.Id == termeId);
        if (terme == null) return;

        var confirmed = await JSRuntime.InvokeAsync<bool>("confirm",
            $"Êtes-vous sûr de vouloir supprimer le terme '{terme.Nom}' ?");

        if (confirmed)
        {
            try
            {
                await TermeGlossaireService.SupprimerAsync(termeId);
                await ChargerDonnees();
            }
            catch (Exception ex)
            {
                Console.Error.WriteLine($"Erreur lors de la suppression: {ex.Message}");
                await JSRuntime.InvokeVoidAsync("alert", "Erreur lors de la suppression du terme.");
            }
        }
    }
}
