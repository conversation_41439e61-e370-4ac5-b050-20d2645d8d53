using DataHubGatineau.Domain.Entites;
using DataHubGatineau.Domain.Interfaces.Repositories;
using DataHubGatineau.Infrastructure.Persistence;
using Microsoft.EntityFrameworkCore;

namespace DataHubGatineau.Infrastructure.Repositories;

/// <summary>
/// Implémentation du dépôt pour les associations entre termes du glossaire et politiques.
/// </summary>
public class DepotTermeGlossairePolitique : DepotBase<TermeGlossairePolitique>, IDepotTermeGlossairePolitique
{
    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DepotTermeGlossairePolitique"/>.
    /// </summary>
    /// <param name="context">Contexte de base de données.</param>
    public DepotTermeGlossairePolitique(CentreDonneesDbContext context) : base(context)
    {
    }

    /// <summary>
    /// Obtient toutes les associations avec les entités liées.
    /// </summary>
    /// <returns>Collection d'associations avec les entités liées.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirTousAvecEntitesLieesAsync()
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .OrderBy(tgp => tgp.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient une association par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association avec les entités liées si trouvée.</returns>
    public async Task<TermeGlossairePolitique?> ObtenirParIdAvecEntitesLieesAsync(Guid id)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .FirstOrDefaultAsync(tgp => tgp.Id == id);
    }

    /// <summary>
    /// Obtient toutes les associations pour un terme du glossaire spécifique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Collection d'associations pour le terme spécifié.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirParTermeGlossaireAsync(Guid termeGlossaireId)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.TermeGlossaireId == termeGlossaireId)
            .OrderBy(tgp => tgp.Priorite)
            .ThenBy(tgp => tgp.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient toutes les associations pour une politique spécifique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Collection d'associations pour la politique spécifiée.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirParPolitiqueAsync(Guid politiqueId)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Where(tgp => tgp.PolitiqueId == politiqueId)
            .OrderBy(tgp => tgp.Priorite)
            .ThenBy(tgp => tgp.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par type d'application.
    /// </summary>
    /// <param name="typeApplication">Type d'application.</param>
    /// <returns>Collection d'associations du type spécifié.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirParTypeApplicationAsync(string typeApplication)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.TypeApplication == typeApplication)
            .OrderBy(tgp => tgp.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par niveau d'impact.
    /// </summary>
    /// <param name="niveauImpact">Niveau d'impact.</param>
    /// <returns>Collection d'associations du niveau spécifié.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirParNiveauImpactAsync(string niveauImpact)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.NiveauImpact == niveauImpact)
            .OrderBy(tgp => tgp.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations actives.
    /// </summary>
    /// <returns>Collection d'associations actives.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirAssociationsActivesAsync()
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.EstActive)
            .OrderBy(tgp => tgp.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations obligatoires.
    /// </summary>
    /// <returns>Collection d'associations obligatoires.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirAssociationsObligatoiresAsync()
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.EstObligatoire)
            .OrderBy(tgp => tgp.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par statut de conformité.
    /// </summary>
    /// <param name="statutConformite">Statut de conformité.</param>
    /// <returns>Collection d'associations avec le statut spécifié.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirParStatutConformiteAsync(string statutConformite)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.StatutConformite == statutConformite)
            .OrderBy(tgp => tgp.DateDerniereEvaluation)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations en vigueur à une date donnée.
    /// </summary>
    /// <param name="date">Date de référence.</param>
    /// <returns>Collection d'associations en vigueur.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirEnVigueurAsync(DateTime? date = null)
    {
        var dateRef = date ?? DateTime.UtcNow;

        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.EstActive &&
                         (!tgp.DateEntreeVigueurSpecifique.HasValue || tgp.DateEntreeVigueurSpecifique <= dateRef) &&
                         (!tgp.DateExpirationSpecifique.HasValue || tgp.DateExpirationSpecifique > dateRef))
            .OrderBy(tgp => tgp.Priorite)
            .ToListAsync();
    }

    /// <summary>
    /// Obtient les associations par niveau de priorité.
    /// </summary>
    /// <param name="priorite">Niveau de priorité (1=Haute, 2=Moyenne, 3=Basse).</param>
    /// <returns>Collection d'associations de la priorité spécifiée.</returns>
    public async Task<IEnumerable<TermeGlossairePolitique>> ObtenirParPrioriteAsync(int priorite)
    {
        return await _context.TermesGlossairePolitiques
            .Include(tgp => tgp.TermeGlossaire)
            .Include(tgp => tgp.Politique)
            .Where(tgp => tgp.Priorite == priorite)
            .OrderBy(tgp => tgp.DateAssociation)
            .ToListAsync();
    }

    /// <summary>
    /// Vérifie si une association existe déjà entre un terme et une politique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>True si l'association existe, false sinon.</returns>
    public async Task<bool> AssociationExisteAsync(Guid termeGlossaireId, Guid politiqueId)
    {
        return await _context.TermesGlossairePolitiques
            .AnyAsync(tgp => tgp.TermeGlossaireId == termeGlossaireId && 
                            tgp.PolitiqueId == politiqueId);
    }

    /// <summary>
    /// Obtient les statistiques d'associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    public async Task<Dictionary<string, int>> ObtenirStatistiquesAsync()
    {
        var stats = new Dictionary<string, int>();

        stats["Total"] = await _context.TermesGlossairePolitiques.CountAsync();
        stats["Actives"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.EstActive);
        stats["Obligatoires"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.EstObligatoire);
        stats["PrioriteHaute"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.Priorite == 1);
        stats["PrioriteMoyenne"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.Priorite == 2);
        stats["PrioriteBasse"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.Priorite == 3);
        stats["Conformes"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.StatutConformite == "Conforme");
        stats["NonConformes"] = await _context.TermesGlossairePolitiques.CountAsync(tgp => tgp.StatutConformite == "Non conforme");

        return stats;
    }

    /// <summary>
    /// Supprime toutes les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    public async Task<int> SupprimerParTermeGlossaireAsync(Guid termeGlossaireId)
    {
        var associations = await _context.TermesGlossairePolitiques
            .Where(tgp => tgp.TermeGlossaireId == termeGlossaireId)
            .ToListAsync();

        _context.TermesGlossairePolitiques.RemoveRange(associations);
        await _context.SaveChangesAsync();

        return associations.Count;
    }

    /// <summary>
    /// Supprime toutes les associations pour une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    public async Task<int> SupprimerParPolitiqueAsync(Guid politiqueId)
    {
        var associations = await _context.TermesGlossairePolitiques
            .Where(tgp => tgp.PolitiqueId == politiqueId)
            .ToListAsync();

        _context.TermesGlossairePolitiques.RemoveRange(associations);
        await _context.SaveChangesAsync();

        return associations.Count;
    }

    /// <summary>
    /// Met à jour le statut de conformité d'une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="statutConformite">Nouveau statut de conformité.</param>
    /// <param name="scoreConformite">Score de conformité (optionnel).</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    public async Task<bool> MettreAJourConformiteAsync(Guid id, string statutConformite, decimal? scoreConformite = null)
    {
        var association = await _context.TermesGlossairePolitiques.FindAsync(id);
        if (association == null) return false;

        association.StatutConformite = statutConformite;
        association.ScoreConformite = scoreConformite;
        association.DateDerniereEvaluation = DateTime.UtcNow;
        association.DateModification = DateTime.UtcNow;
        
        await _context.SaveChangesAsync();
        return true;
    }
}
