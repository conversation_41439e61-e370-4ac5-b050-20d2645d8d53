using System.ComponentModel.DataAnnotations;

namespace DataHubGatineau.Domain.Entites;

/// <summary>
/// Représente l'association entre un terme du glossaire et une règle de qualité.
/// Cette entité permet d'associer des règles de qualité à des termes métier spécifiques.
/// </summary>
public class TermeGlossaireRegleQualite : EntiteBase
{
    /// <summary>
    /// Obtient ou définit l'identifiant du terme du glossaire.
    /// </summary>
    [Required]
    public Guid TermeGlossaireId { get; set; }

    /// <summary>
    /// Obtient ou définit le terme du glossaire associé.
    /// </summary>
    public virtual TermeGlossaire TermeGlossaire { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit l'identifiant de la règle de qualité.
    /// </summary>
    [Required]
    public Guid RegleQualiteId { get; set; }

    /// <summary>
    /// Obtient ou définit la règle de qualité associée.
    /// </summary>
    public virtual RegleQualite RegleQualite { get; set; } = null!;

    /// <summary>
    /// Obtient ou définit le type de validation que cette règle effectue pour le terme.
    /// </summary>
    [Required]
    [StringLength(50)]
    public string TypeValidation { get; set; } = "Conformite";

    /// <summary>
    /// Obtient ou définit l'utilisateur qui a créé cette association.
    /// </summary>
    [StringLength(100)]
    public string? UtilisateurAssociation { get; set; }

    /// <summary>
    /// Obtient ou définit la date de création de l'association.
    /// </summary>
    public DateTime DateAssociation { get; set; } = DateTime.UtcNow;

    /// <summary>
    /// Obtient ou définit si cette règle est obligatoire pour le terme.
    /// </summary>
    public bool EstObligatoire { get; set; } = false;

    /// <summary>
    /// Obtient ou définit la priorité de cette règle pour le terme (1 = haute, 5 = basse).
    /// </summary>
    public int Priorite { get; set; } = 3;

    /// <summary>
    /// Obtient ou définit le seuil spécifique pour ce terme (peut surcharger le seuil de la règle).
    /// </summary>
    public decimal? SeuilSpecifique { get; set; }

    /// <summary>
    /// Obtient ou définit des commentaires sur l'association.
    /// </summary>
    [StringLength(500)]
    public string? Commentaires { get; set; }

    /// <summary>
    /// Obtient ou définit si cette association est active.
    /// </summary>
    public bool EstActive { get; set; } = true;
}

/// <summary>
/// Énumération des types de validation possibles entre un terme et une règle de qualité.
/// </summary>
public enum TypeValidationTermeRegle
{
    /// <summary>
    /// Validation de conformité aux définitions du terme.
    /// </summary>
    Conformite,

    /// <summary>
    /// Validation de complétude des données selon le terme.
    /// </summary>
    Completude,

    /// <summary>
    /// Validation de l'exactitude des données selon le terme.
    /// </summary>
    Exactitude,

    /// <summary>
    /// Validation de la cohérence des données selon le terme.
    /// </summary>
    Coherence,

    /// <summary>
    /// Validation de l'unicité des données selon le terme.
    /// </summary>
    Unicite,

    /// <summary>
    /// Validation de la validité des données selon le terme.
    /// </summary>
    Validite,

    /// <summary>
    /// Validation de la fraîcheur des données selon le terme.
    /// </summary>
    Fraicheur,

    /// <summary>
    /// Validation personnalisée selon les besoins du terme.
    /// </summary>
    Personnalisee
}
