using Asp.Versioning;
using DataHubGatineau.Application.Interfaces;
using DataHubGatineau.Domain.Entites;
using Microsoft.AspNetCore.Mvc;

namespace DataHubGatineau.API.Controllers;

/// <summary>
/// Contrôleur pour les opérations sur les domaines d'affaires.
/// </summary>
[ApiVersion("1.0")]
[Route("api/v{version:apiVersion}/DomainesAffaires")]
public class DomainesAffairesController : ApiControllerBase
{
    private readonly IServiceDomaineAffaires _serviceDomaineAffaires;

    /// <summary>
    /// Initialise une nouvelle instance de la classe <see cref="DomainesAffairesController"/>.
    /// </summary>
    /// <param name="serviceDomaineAffaires">Service des domaines d'affaires.</param>
    public DomainesAffairesController(IServiceDomaineAffaires serviceDomaineAffaires)
    {
        _serviceDomaineAffaires = serviceDomaineAffaires;
    }

    /// <summary>
    /// Obtient tous les domaines d'affaires.
    /// </summary>
    /// <returns>Une collection de domaines d'affaires.</returns>
    [HttpGet]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<DomaineAffaires>>> ObtenirTous()
    {
        var domaines = await _serviceDomaineAffaires.ObtenirTousAsync();
        return Ok(domaines);
    }

    /// <summary>
    /// Obtient tous les domaines d'affaires actifs.
    /// </summary>
    /// <returns>Une collection de domaines d'affaires actifs.</returns>
    [HttpGet("actifs")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<IEnumerable<DomaineAffaires>>> ObtenirActifs()
    {
        var domaines = await _serviceDomaineAffaires.ObtenirActifsAsync();
        return Ok(domaines);
    }

    /// <summary>
    /// Obtient un domaine d'affaires par son identifiant.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <returns>Le domaine d'affaires si trouvé.</returns>
    [HttpGet("{id}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DomaineAffaires>> ObtenirParId(Guid id)
    {
        var domaine = await _serviceDomaineAffaires.ObtenirParIdAsync(id);
        if (domaine == null)
        {
            return NotFound();
        }

        return Ok(domaine);
    }

    /// <summary>
    /// Obtient un domaine d'affaires par son code.
    /// </summary>
    /// <param name="code">Code du domaine d'affaires.</param>
    /// <returns>Le domaine d'affaires si trouvé.</returns>
    [HttpGet("code/{code}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<DomaineAffaires>> ObtenirParCode(string code)
    {
        var domaine = await _serviceDomaineAffaires.ObtenirParCodeAsync(code);
        if (domaine == null)
        {
            return NotFound();
        }

        return Ok(domaine);
    }

    /// <summary>
    /// Obtient les statistiques d'un domaine d'affaires.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <returns>Les statistiques du domaine d'affaires.</returns>
    [HttpGet("{id}/statistiques")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<ActionResult<StatistiquesDomaineAffaires>> ObtenirStatistiques(Guid id)
    {
        var domaine = await _serviceDomaineAffaires.ObtenirParIdAsync(id);
        if (domaine == null)
        {
            return NotFound();
        }

        var statistiques = await _serviceDomaineAffaires.ObtenirStatistiquesAsync(id);
        return Ok(statistiques);
    }

    /// <summary>
    /// Ajoute un nouveau domaine d'affaires.
    /// </summary>
    /// <param name="domaine">Domaine d'affaires à ajouter.</param>
    /// <returns>Le domaine d'affaires ajouté.</returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status201Created)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    public async Task<ActionResult<DomaineAffaires>> Ajouter(DomaineAffaires domaine)
    {
        if (domaine == null)
        {
            return BadRequest();
        }

        try
        {
            var domaineAjoute = await _serviceDomaineAffaires.AjouterAsync(domaine);
            return CreatedAtAction(nameof(ObtenirParId), new { id = domaineAjoute.Id }, domaineAjoute);
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Met à jour un domaine d'affaires existant.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <param name="domaine">Domaine d'affaires à mettre à jour.</param>
    /// <returns>Aucun contenu si la mise à jour est réussie.</returns>
    [HttpPut("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status400BadRequest)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> MettreAJour(Guid id, DomaineAffaires domaine)
    {
        if (domaine == null || id != domaine.Id)
        {
            return BadRequest();
        }

        var domaineExistant = await _serviceDomaineAffaires.ObtenirParIdAsync(id);
        if (domaineExistant == null)
        {
            return NotFound();
        }

        try
        {
            await _serviceDomaineAffaires.MettreAJourAsync(id, domaine);
            return NoContent();
        }
        catch (InvalidOperationException ex)
        {
            return BadRequest(ex.Message);
        }
    }

    /// <summary>
    /// Change l'état d'activation d'un domaine d'affaires.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <param name="estActif">Nouvel état d'activation.</param>
    /// <returns>Aucun contenu si l'opération est réussie.</returns>
    [HttpPatch("{id}/activation")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> ChangerEtatActivation(Guid id, [FromBody] bool estActif)
    {
        var resultat = await _serviceDomaineAffaires.ChangerEtatActivationAsync(id, estActif);
        if (!resultat)
        {
            return NotFound();
        }

        return NoContent();
    }

    /// <summary>
    /// Supprime un domaine d'affaires.
    /// </summary>
    /// <param name="id">Identifiant du domaine d'affaires.</param>
    /// <returns>Aucun contenu si la suppression est réussie.</returns>
    [HttpDelete("{id}")]
    [ProducesResponseType(StatusCodes.Status204NoContent)]
    [ProducesResponseType(StatusCodes.Status404NotFound)]
    public async Task<IActionResult> Supprimer(Guid id)
    {
        var domaineExistant = await _serviceDomaineAffaires.ObtenirParIdAsync(id);
        if (domaineExistant == null)
        {
            return NotFound();
        }

        await _serviceDomaineAffaires.SupprimerAsync(id);
        return NoContent();
    }

    /// <summary>
    /// Vérifie si un nom de domaine d'affaires existe.
    /// </summary>
    /// <param name="nom">Nom à vérifier.</param>
    /// <param name="idExclure">ID à exclure de la vérification.</param>
    /// <returns>True si le nom existe, false sinon.</returns>
    [HttpGet("verifier-nom/{nom}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<bool>> VerifierNomExiste(string nom, [FromQuery] Guid? idExclure = null)
    {
        var existe = await _serviceDomaineAffaires.NomExisteAsync(nom, idExclure);
        return Ok(existe);
    }

    /// <summary>
    /// Vérifie si un code de domaine d'affaires existe.
    /// </summary>
    /// <param name="code">Code à vérifier.</param>
    /// <param name="idExclure">ID à exclure de la vérification.</param>
    /// <returns>True si le code existe, false sinon.</returns>
    [HttpGet("verifier-code/{code}")]
    [ProducesResponseType(StatusCodes.Status200OK)]
    public async Task<ActionResult<bool>> VerifierCodeExiste(string code, [FromQuery] Guid? idExclure = null)
    {
        var existe = await _serviceDomaineAffaires.CodeExisteAsync(code, idExclure);
        return Ok(existe);
    }
}
