# Guide d'Utilisation des API du Glossaire - DataHub Gatineau

## 🎯 **Objectif**

Ce guide fournit des exemples pratiques d'utilisation des nouvelles API pour les intégrations du glossaire. Il s'adresse aux développeurs et intégrateurs qui souhaitent exploiter les fonctionnalités de gouvernance des données.

## 🔧 **Configuration Préalable**

### **URL de Base**
```
https://localhost:5001/api/v1/
```

### **Authentification**
```http
Authorization: Bearer {token}
Content-Type: application/json
```

## 📋 **API des Politiques**

### **1. Créer une Politique de Gouvernance**

```http
POST /api/v1/politiques
```

**Corps de la requête :**
```json
{
  "code": "POL-QUAL-001",
  "titre": "Politique de Qualité des Données Client",
  "description": "Définit les standards de qualité pour les données clients",
  "categorie": "Qualité",
  "statut": "Brouillon",
  "niveauApplication": "Département",
  "contenu": "Les données clients doivent respecter les critères suivants:\n1. Complétude > 95%\n2. Exactitude > 98%\n3. Cohérence > 99%",
  "motsCles": "qualité, client, données, standard",
  "dateEntreeVigueur": "2025-07-01T00:00:00Z",
  "dateExpiration": "2026-06-30T23:59:59Z",
  "estActive": true
}
```

**Réponse :**
```json
{
  "id": "123e4567-e89b-12d3-a456-426614174000",
  "code": "POL-QUAL-001",
  "titre": "Politique de Qualité des Données Client",
  "statut": "Brouillon",
  "dateCreation": "2025-06-02T10:30:00Z",
  "dateModification": "2025-06-02T10:30:00Z"
}
```

### **2. Rechercher des Politiques**

```http
GET /api/v1/politiques/rechercher?motsCles=qualité client
```

**Réponse :**
```json
[
  {
    "id": "123e4567-e89b-12d3-a456-426614174000",
    "code": "POL-QUAL-001",
    "titre": "Politique de Qualité des Données Client",
    "description": "Définit les standards de qualité...",
    "categorie": "Qualité",
    "statut": "Active"
  }
]
```

### **3. Obtenir les Statistiques des Politiques**

```http
GET /api/v1/politiques/statistiques
```

**Réponse :**
```json
{
  "Total": 25,
  "Actives": 18,
  "Brouillon": 3,
  "EnRevision": 2,
  "EnAttenteApprobation": 1,
  "Archivees": 1,
  "EnVigueur": 16,
  "ExpirantBientot": 2,
  "AReviser": 1
}
```

## 🔗 **API des Associations Terme-Métadonnée**

### **1. Créer une Association**

```http
POST /api/v1/associationsterme-metadonnee
```

**Corps de la requête :**
```json
{
  "termeGlossaireId": "terme-client-guid",
  "metadonneeId": "meta-nom-client-guid",
  "typeAssociation": "Manuel",
  "description": "Le terme 'Client' correspond à la métadonnée 'Nom du Client'",
  "contexteUtilisation": "Identification des entités client",
  "estValidee": true,
  "validePar": "<EMAIL>",
  "dateValidation": "2025-06-02T14:30:00Z"
}
```

### **2. Obtenir les Suggestions Automatiques**

```http
GET /api/v1/associationsterme-metadonnee/suggestions?scoreMinimum=0.8
```

**Réponse :**
```json
[
  {
    "id": "suggestion-guid",
    "termeGlossaire": {
      "nom": "Adresse",
      "definition": "Localisation géographique d'une entité"
    },
    "metadonnee": {
      "nom": "ADRESSE_COMPLETE",
      "description": "Adresse complète du client"
    },
    "confianceScore": 0.92,
    "typeAssociation": "Suggere",
    "raisonSuggestion": "Similarité sémantique élevée"
  }
]
```

### **3. Valider une Association**

```http
PUT /api/v1/associationsterme-metadonnee/{id}
```

**Corps de la requête :**
```json
{
  "id": "association-guid",
  "estValidee": true,
  "validePar": "<EMAIL>",
  "commentaireValidation": "Association confirmée après vérification métier"
}
```

## ⚡ **API des Associations Terme-Règle Qualité**

### **1. Associer un Terme à une Règle de Qualité**

```http
POST /api/v1/associationsterme-reglequalite
```

**Corps de la requête :**
```json
{
  "termeGlossaireId": "terme-email-guid",
  "regleQualiteId": "regle-format-email-guid",
  "typeValidation": "Format",
  "priorite": 1,
  "estObligatoire": true,
  "estActive": true,
  "seuilSpecifique": 99.5,
  "description": "Validation du format email pour le terme 'Adresse Email'"
}
```

### **2. Obtenir les Associations par Priorité**

```http
GET /api/v1/associationsterme-reglequalite/priorite/1
```

**Réponse :**
```json
[
  {
    "id": "assoc-guid",
    "termeGlossaire": {
      "nom": "Numéro de Téléphone",
      "definition": "Numéro de contact téléphonique"
    },
    "regleQualite": {
      "nom": "Format Téléphone Canadien",
      "description": "Validation du format téléphonique canadien"
    },
    "priorite": 1,
    "estObligatoire": true,
    "seuilSpecifique": 100.0
  }
]
```

### **3. Activer/Désactiver une Association**

```http
PATCH /api/v1/associationsterme-reglequalite/{id}/activation
```

**Corps de la requête :**
```json
{
  "estActive": false
}
```

## 🏛️ **API des Associations Terme-Politique**

### **1. Associer un Terme à une Politique**

```http
POST /api/v1/associationsterme-politique
```

**Corps de la requête :**
```json
{
  "termeGlossaireId": "terme-donnee-personnelle-guid",
  "politiqueId": "politique-rgpd-guid",
  "typeApplication": "Obligatoire",
  "niveauImpact": "Élevé",
  "priorite": 1,
  "estObligatoire": true,
  "estActive": true,
  "statutConformite": "Conforme",
  "scoreConformite": 95.5,
  "description": "Application de la politique RGPD aux données personnelles"
}
```

### **2. Obtenir les Associations par Statut de Conformité**

```http
GET /api/v1/associationsterme-politique/statut-conformite/Non conforme
```

**Réponse :**
```json
[
  {
    "id": "assoc-guid",
    "termeGlossaire": {
      "nom": "Données Sensibles",
      "definition": "Informations nécessitant une protection renforcée"
    },
    "politique": {
      "code": "POL-SEC-001",
      "titre": "Politique de Sécurité des Données"
    },
    "statutConformite": "Non conforme",
    "scoreConformite": 65.0,
    "dateDerniereEvaluation": "2025-05-15T09:00:00Z"
  }
]
```

### **3. Mettre à Jour la Conformité**

```http
PATCH /api/v1/associationsterme-politique/{id}/conformite
```

**Corps de la requête :**
```json
{
  "statutConformite": "Conforme",
  "scoreConformite": 98.5
}
```

## 📊 **Exemples de Workflows Complets**

### **Workflow 1 : Création d'une Politique Complète**

```bash
# 1. Créer la politique
curl -X POST "https://localhost:5001/api/v1/politiques" \
  -H "Content-Type: application/json" \
  -d '{
    "code": "POL-GDPR-002",
    "titre": "Gestion des Consentements",
    "categorie": "Confidentialité",
    "statut": "Brouillon"
  }'

# 2. Associer aux termes concernés
curl -X POST "https://localhost:5001/api/v1/associationsterme-politique" \
  -H "Content-Type: application/json" \
  -d '{
    "termeGlossaireId": "terme-consentement-guid",
    "politiqueId": "politique-guid-retourne",
    "typeApplication": "Obligatoire"
  }'

# 3. Activer la politique
curl -X PATCH "https://localhost:5001/api/v1/politiques/{id}/statut" \
  -H "Content-Type: application/json" \
  -d '"Active"'
```

### **Workflow 2 : Audit de Conformité**

```bash
# 1. Obtenir toutes les associations non conformes
curl "https://localhost:5001/api/v1/associationsterme-politique/statut-conformite/Non conforme"

# 2. Obtenir les statistiques globales
curl "https://localhost:5001/api/v1/associationsterme-politique/statistiques"

# 3. Obtenir les politiques expirant bientôt
curl "https://localhost:5001/api/v1/politiques/expirant-bientot?joursAvantExpiration=30"
```

## 🔍 **Codes d'Erreur Courants**

| Code | Description | Solution |
|------|-------------|----------|
| 400 | Données invalides | Vérifier le format JSON et les champs requis |
| 404 | Ressource non trouvée | Vérifier les GUID fournis |
| 409 | Association déjà existante | Utiliser PUT pour mettre à jour |
| 422 | Validation métier échouée | Vérifier les règles de validation |

## 📚 **Ressources Supplémentaires**

- [Documentation Swagger](../swagger/index.html)
- [Schémas JSON](../schemas/)
- [Exemples Postman](../postman/DataHub_Glossaire.json)
- [Guide de Déploiement](deploiement.md)

---

**Dernière mise à jour** : 02 juin 2025  
**Version** : 1.0  
**Auteur** : Équipe DataHub Gatineau
