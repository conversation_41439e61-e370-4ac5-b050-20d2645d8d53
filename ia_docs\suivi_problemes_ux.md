# Suivi des Problèmes UX et Fonctionnalités - DataHub Gatineau

## Date: 8 juin 2025

### Problèmes Identifiés dans les Pages de Détails et Édition d'Actifs

#### 1. **Bouton manquant pour "Produits de données"**
- **Localisation**: Page de détails des actifs
- **Problème**: Il y a un bouton "Ajouter une règle" et "Soumettre pour approbation" mais pas de bouton pour ajouter des produits de données
- **Impact**: Incohérence dans l'interface utilisateur
- **Priorité**: Moyenne
- **Statut**: À analyser

#### 2. **Suppression de métadonnées sans confirmation**
- **Localisation**: Page de détails des actifs - section "Métadonnées personnalisées"
- **Problème**: 
  - Suppression directe sans dialogue de confirmation professionnel
  - Question: Les métadonnées générées automatiquement après un scan devraient-elles être supprimables?
  - Question: Qui devrait avoir le droit de supprimer les métadonnées?
- **Impact**: Risque de suppression accidentelle de données importantes
- **Priorité**: Haute (sécurité des données)
- **Statut**: À analyser et implémenter

#### 3. **Ajout de métadonnées personnalisées dans la page de détails**
- **Localisation**: Page de détails des actifs
- **Question**: Devrait-on permettre l'ajout de métadonnées dans la page de détails ou seulement dans la page de modification?
- **Considération**: Cohérence UX et workflow de gouvernance
- **Priorité**: Moyenne
- **Statut**: À décider

#### 4. **Champ "Chemin d'accès" - Utilité**
- **Localisation**: Page d'édition d'actifs - onglet Métadonnées
- **Question**: À quoi sert ce champ? Est-il nécessaire?
- **Recherche nécessaire**: Vérifier l'utilisation dans Collibra et Purview
- **Priorité**: Basse
- **Statut**: À rechercher

#### 5. **Fonctionnalité "Termes du glossaire associés" défaillante**
- **Localisation**: Page d'édition d'actifs
- **Problèmes identifiés**:
  - Bouton "+" pour ajouter ne fonctionne pas
  - L'ajout de termes de glossaire ne persiste pas en base de données
  - Édition de termes du glossaire potentiellement défaillante
- **Impact**: Fonctionnalité de gouvernance critique non opérationnelle
- **Priorité**: Très haute
- **Statut**: À vérifier et corriger

#### 6. **Workflow d'approbation manquant**
- **Localisation**: Pages d'ajout et d'édition d'actifs
- **Problème**: Deux boutons présents ("Enregistrer comme brouillon" et "Soumettre pour approbation") mais pas de workflow d'approbation implémenté
- **Questions**:
  - Faut-il implémenter la fonctionnalité "Soumettre pour approbation"?
  - Quels workflows sont nécessaires?
  - Qui sont les approbateurs?
  - Quels sont les états possibles d'un actif?
- **Impact**: Gouvernance des données incomplète
- **Priorité**: Haute (gouvernance)
- **Statut**: À analyser et planifier

### Actions Recommandées

#### Immédiat (Priorité Très Haute)
1. **Vérifier et corriger la fonctionnalité des termes du glossaire**
   - Tester l'ajout de termes
   - Vérifier la persistance en base de données
   - Corriger l'édition de termes

#### Court terme (Priorité Haute)
1. **Implémenter dialogue de confirmation pour suppression de métadonnées**
2. **Analyser et planifier le workflow d'approbation**
3. **Définir les règles de suppression de métadonnées**

#### Moyen terme (Priorité Moyenne)
1. **Ajouter bouton pour produits de données**
2. **Décider sur l'ajout de métadonnées dans page de détails**

#### Long terme (Priorité Basse)
1. **Rechercher l'utilité du champ "Chemin d'accès"**

### Notes de Recherche

#### Standards de l'industrie à consulter:
- **Collibra**: Workflow d'approbation, gestion des métadonnées
- **Purview**: Champs standards, bonnes pratiques
- **Autres solutions**: Alation, Informatica, etc.

### Historique des Modifications
- **8 juin 2025**: Création du document avec 6 problèmes identifiés
