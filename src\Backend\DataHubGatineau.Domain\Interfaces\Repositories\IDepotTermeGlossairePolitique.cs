using DataHubGatineau.Domain.Entites;

namespace DataHubGatineau.Domain.Interfaces.Repositories;

/// <summary>
/// Interface du dépôt pour les associations entre termes du glossaire et politiques.
/// </summary>
public interface IDepotTermeGlossairePolitique : IDepotBase<TermeGlossairePolitique>
{
    /// <summary>
    /// Obtient toutes les associations avec les entités liées.
    /// </summary>
    /// <returns>Collection d'associations avec les entités liées.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirTousAvecEntitesLieesAsync();

    /// <summary>
    /// Obtient une association par identifiant avec les entités liées.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <returns>Association avec les entités liées si trouvée.</returns>
    Task<TermeGlossairePolitique?> ObtenirParIdAvecEntitesLieesAsync(Guid id);

    /// <summary>
    /// Obtient toutes les associations pour un terme du glossaire spécifique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Collection d'associations pour le terme spécifié.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirParTermeGlossaireAsync(Guid termeGlossaireId);

    /// <summary>
    /// Obtient toutes les associations pour une politique spécifique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Collection d'associations pour la politique spécifiée.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirParPolitiqueAsync(Guid politiqueId);

    /// <summary>
    /// Obtient les associations par type d'application.
    /// </summary>
    /// <param name="typeApplication">Type d'application.</param>
    /// <returns>Collection d'associations du type spécifié.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirParTypeApplicationAsync(string typeApplication);

    /// <summary>
    /// Obtient les associations par niveau d'impact.
    /// </summary>
    /// <param name="niveauImpact">Niveau d'impact.</param>
    /// <returns>Collection d'associations du niveau spécifié.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirParNiveauImpactAsync(string niveauImpact);

    /// <summary>
    /// Obtient les associations actives.
    /// </summary>
    /// <returns>Collection d'associations actives.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirAssociationsActivesAsync();

    /// <summary>
    /// Obtient les associations obligatoires.
    /// </summary>
    /// <returns>Collection d'associations obligatoires.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirAssociationsObligatoiresAsync();

    /// <summary>
    /// Obtient les associations par statut de conformité.
    /// </summary>
    /// <param name="statutConformite">Statut de conformité.</param>
    /// <returns>Collection d'associations avec le statut spécifié.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirParStatutConformiteAsync(string statutConformite);

    /// <summary>
    /// Obtient les associations en vigueur à une date donnée.
    /// </summary>
    /// <param name="date">Date de référence.</param>
    /// <returns>Collection d'associations en vigueur.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirEnVigueurAsync(DateTime? date = null);

    /// <summary>
    /// Obtient les associations par niveau de priorité.
    /// </summary>
    /// <param name="priorite">Niveau de priorité (1=Haute, 2=Moyenne, 3=Basse).</param>
    /// <returns>Collection d'associations de la priorité spécifiée.</returns>
    Task<IEnumerable<TermeGlossairePolitique>> ObtenirParPrioriteAsync(int priorite);

    /// <summary>
    /// Vérifie si une association existe déjà entre un terme et une politique.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>True si l'association existe, false sinon.</returns>
    Task<bool> AssociationExisteAsync(Guid termeGlossaireId, Guid politiqueId);

    /// <summary>
    /// Obtient les statistiques d'associations.
    /// </summary>
    /// <returns>Dictionnaire avec les statistiques.</returns>
    Task<Dictionary<string, int>> ObtenirStatistiquesAsync();

    /// <summary>
    /// Supprime toutes les associations pour un terme du glossaire.
    /// </summary>
    /// <param name="termeGlossaireId">Identifiant du terme du glossaire.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    Task<int> SupprimerParTermeGlossaireAsync(Guid termeGlossaireId);

    /// <summary>
    /// Supprime toutes les associations pour une politique.
    /// </summary>
    /// <param name="politiqueId">Identifiant de la politique.</param>
    /// <returns>Nombre d'associations supprimées.</returns>
    Task<int> SupprimerParPolitiqueAsync(Guid politiqueId);

    /// <summary>
    /// Met à jour le statut de conformité d'une association.
    /// </summary>
    /// <param name="id">Identifiant de l'association.</param>
    /// <param name="statutConformite">Nouveau statut de conformité.</param>
    /// <param name="scoreConformite">Score de conformité (optionnel).</param>
    /// <returns>True si la mise à jour a réussi.</returns>
    Task<bool> MettreAJourConformiteAsync(Guid id, string statutConformite, decimal? scoreConformite = null);
}
